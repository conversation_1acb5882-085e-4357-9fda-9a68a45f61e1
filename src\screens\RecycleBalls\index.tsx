import React, {useState} from 'react';
import {View, TouchableOpacity, ScrollView} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {Header, Icon, SafeAreaView} from '@/components';
import {OfferBanner} from '@/components/common/OfferBanner';
import SearchInput from '@/components/SearchInput';
import {styles as createStyles} from './styles';
import Typography from '@/components/Typography';

const RecycleBallsScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle="Recycle Balls"
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />

      <View style={styles.footer}>
        <OfferBanner text="Invite friends, get 10% off" />
      </View>
    </SafeAreaView>
  );
};

export default RecycleBallsScreen;
