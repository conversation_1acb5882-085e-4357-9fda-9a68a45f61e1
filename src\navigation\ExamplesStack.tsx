import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import OptimizedFlatListScreen from '@/screens/examples/OptimizedFlatListScreen';
import SentryTestScreen from '@/screens/examples/SentryTestScreen';

export type ExamplesStackParamList = {
  OptimizedFlatList: undefined;
  SentryTest: undefined;
  // Add more example screens here as needed
};

const Stack = createNativeStackNavigator<ExamplesStackParamList>();

const ExamplesStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="OptimizedFlatList"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true, // Enable gesture navigation
        gestureDirection: 'horizontal', // iOS style (default)
        gestureResponseDistance: {start: 50, end: -1}, // Distance from edge to trigger gesture
      }}>
      <Stack.Screen name="SentryTest" component={SentryTestScreen} options={{animation: 'fade'}} />
      <Stack.Screen
        name="OptimizedFlatList"
        component={OptimizedFlatListScreen}
        options={{animation: 'fade'}}
      />
      {/* Add more example screens here as needed */}
    </Stack.Navigator>
  );
};

export default ExamplesStack;
