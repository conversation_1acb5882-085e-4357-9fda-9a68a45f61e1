import React, {useEffect, useState} from 'react';
import {Text, TouchableOpacity, View, StyleSheet} from 'react-native';
import {useAuthStore, useThemeStore} from '@/store';
import {CButton, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useResendOtp, useVerify} from '@/hooks/queries/useAuth';
import {OtpInput} from 'react-native-otp-entry';
import {toaster} from '@/utils/commonFunctions';

export const OTP_SCREEN_TIMEOUT = 5 * 60; // 5 minutes

const Verification = ({route}: {route: any}) => {
  const {email} = route.params;
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const theme = useThemeStore();
  const {isApiStatus} = useAuthStore();

  const inputSpacing = 10;

  const verifyOtpApi = useVerify();
  const resendOtpApi = useResendOtp();

  const [timer, setTimer] = useState(OTP_SCREEN_TIMEOUT);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [otp, setOtp] = useState('');

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isResendDisabled) {
      interval = setInterval(() => {
        setTimer(prev => {
          if (prev <= 1) {
            clearInterval(interval!);
            setIsResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isResendDisabled]);

  const formatTime = (seconds: number) => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  };

  const onSubmit = (code: string) => {
    if (isApiStatus) {
      verifyOtpApi.mutate(
        {email: email, code: code},
        {
          onSuccess: response => {
            if (response?.status) {
              navigation.replace('Login', {
                email: response?.data?.email || email,
              });
            } else {
              toaster('error', response?.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      const mockUserData = {
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
      };
      useAuthStore.getState().login({user: mockUserData});
    }
  };

  const handleResend = () => {
    setIsResendDisabled(true);
    setOtp('');
    if (timer === 0) {
      resendOtp();
    }
  };

  const resendOtp = () => {
    if (isApiStatus) {
      resendOtpApi.mutate(
        {email: email},
        {
          onSuccess: response => {
            if (response?.status) {
              setTimer(OTP_SCREEN_TIMEOUT);
              toaster('success', response?.message, 'top');
            } else {
              toaster('error', response?.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    }
  };

  return (
    <SafeAreaView includeTop style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          activeOpacity={0.7}
          hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
          <Icon name="Left-chevron" size={22} color={theme.colors.gray} />
        </TouchableOpacity>
        <Typography
          variant="subtitle"
          style={[
            styles.titleText,
            {
              color: theme.colors.text,
              fontSize: theme.fontSize[theme.fontSize.default] + 8,
            },
          ]}>
          Verification
        </Typography>
      </View>

      {/* Title */}
      <Typography
        variant="title"
        style={[
          styles.subtitle,
          {
            color: theme.colors.white,
            fontSize: theme.fontSize.xxlarge,
          },
        ]}>
        Enter 6-digit Code
      </Typography>

      {/* OTP Inputs */}
      <View style={[styles.otpRow, {gap: inputSpacing}]}>
        <OtpInput
          // ref={otpRef}
          numberOfDigits={6}
          focusColor={theme.colors.activeColor}
          autoFocus={true}
          blurOnFilled={true}
          disabled={false}
          type="numeric"
          secureTextEntry={false}
          focusStickBlinkingDuration={500}
          onTextChange={text => setOtp(text)}
          onFilled={text => onSubmit(text)}
          theme={{
            pinCodeContainerStyle: {
              borderColor: theme.colors.activeColor,
              width: 50,
            },
            pinCodeTextStyle: {color: theme.colors.activeColor},
          }}
        />
      </View>

      {/* Resend */}
      <TouchableOpacity
        onPress={handleResend}
        disabled={isResendDisabled}
        style={styles.resendContainer}>
        <Text
          style={{
            color: isResendDisabled ? theme.colors.white2 : theme.colors.primary,
            fontSize: theme.fontSize.medium,
          }}>
          {isResendDisabled ? `Resend Code in ${formatTime(timer)}` : 'Resend Code'}
        </Text>
      </TouchableOpacity>

      {/* Confirm Button */}
      <CButton
        title="Confirm"
        variant="primary"
        onPress={() => onSubmit(otp)}
        isDisabled={otp.length < 6}
        loading={verifyOtpApi.isPending}
      />
    </SafeAreaView>
  );
};

export default Verification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 30,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 20,
    gap: 10,
  },
  backButton: {
    padding: 5,
  },
  titleText: {
    fontWeight: 'bold',
  },
  subtitle: {
    marginBottom: 10,
  },
  otpRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  otpInput: {
    borderWidth: 1,
    borderRadius: 10,
    textAlign: 'center',
    fontSize: 20,
  },
  resendContainer: {
    alignSelf: 'flex-end',
    marginBottom: 10,
    padding: 2,
  },
});
