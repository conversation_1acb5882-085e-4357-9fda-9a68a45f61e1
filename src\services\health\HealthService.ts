import {Platform} from 'react-native';
import {HealthServiceInterface} from './types';
import AndroidHealthService from './AndroidHealthService';
import IOSHealthService from './IOSHealthService';

/**
 * Factory function to create the appropriate health service based on platform
 */
export const createHealthService = (): HealthServiceInterface => {
  try {
    if (Platform.OS === 'android') {
      return new AndroidHealthService();
    } else if (Platform.OS === 'ios') {
      return new IOSHealthService();
    } else {
      console.error('Unsupported platform for health services:', Platform.OS);
      // Return a dummy implementation for unsupported platforms
      return createDummyHealthService();
    }
  } catch (error) {
    console.error('Error creating health service:', error);
    // Return a dummy implementation if there's an error
    return createDummyHealthService();
  }
};

/**
 * Create a dummy health service that returns default values
 * This is used as a fallback when the platform is not supported
 * or when there's an error creating the real health service
 */
const createDummyHealthService = (): HealthServiceInterface => {
  return {
    initialize: async () => false,
    requestPermissions: async () => false,
    hasPermissions: async () => false,
    getStepCount: async (date = new Date()) => ({
      count: 0,
      date: date.toISOString().split('T')[0],
    }),
    getHeartRate: async (date = new Date()) => ({
      value: 0,
      unit: 'bpm',
      date: date.toISOString().split('T')[0],
    }),
    getStepCountGoal: async () => 10000,
    setStepCountGoal: async () => false,
  };
};

// Create a singleton instance of the health service
const healthService = createHealthService();

export default healthService;
