import React from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
  SafeAreaView,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import Carousel, {ICarouselInstance, Pagination} from 'react-native-reanimated-carousel';
import {useSharedValue} from 'react-native-reanimated';
import CommunityCard from '@/components/CommunityCard';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;
import {Images} from '@/config';
import {CImage} from '@/components';
import MediaCard from '@/components/MediaCard';

const GoLife: React.FC = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();

  const data = [
    {image: 'https://picsum.photos/200/300'},
    {image: 'https://picsum.photos/200/300'},
    {image: 'https://picsum.photos/200/300'},
  ];
  const progress = useSharedValue<number>(0);
  const ref = React.useRef<ICarouselInstance>(null);
  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      count: index - progress.value,
      animated: true,
    });
  };
  const dummyArray = [
    {
      image: 'https://picsum.photos/200/300',
      title: 'Volley exercises for beginners',
      description: 'for better performance at the net!',
    },
    {
      image: 'https://picsum.photos/200/300',
      title: 'The perfect drill',
      description: 'to build on-court speed and conditioning Go!',
    },
    {
      image: 'https://picsum.photos/200/300',
      title: 'The perfect drill',
      description: 'to build on-court speed and conditioning Go!',
    },
  ];
  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.content}>
          <Carousel
            ref={ref}
            width={Dimensions.get('screen').width - 45}
            height={Dimensions.get('screen').height * 0.25}
            onProgressChange={progress}
            data={data}
            autoPlay
            autoPlayInterval={3000}
            renderItem={({item}) => (
              <MediaCard
                source={{uri: item?.image}}
                type="image"
                onLikePress={() => {}}
                onCommentPress={() => {}}
                onUploadPress={() => {}}
              />
            )}
          />
          <Pagination.Basic
            progress={progress}
            data={data}
            dotStyle={{backgroundColor: theme.colors.gray, borderRadius: 50}}
            activeDotStyle={{backgroundColor: theme.colors.activeColor, borderRadius: 50}}
            containerStyle={{gap: 5, marginTop: -15}}
            onPress={onPressPagination}
          />
          <View style={styles.cardRow}>
            <TouchableOpacity style={styles.cardItem} onPress={() => navigation.navigate('GoFit')}>
              <View style={styles.cardIconContainer}>
                <CImage source={Images.gym} style={styles.cardIcon} resizeMode="contain" />
              </View>
              <Typography variant="body" style={styles.cardLabel}>
                GoFit
              </Typography>
            </TouchableOpacity>
            <TouchableOpacity style={styles.cardItem} onPress={() => navigation.navigate('GoEats')}>
              <View style={styles.cardIconContainer}>
                <CImage source={Images.goEat} style={styles.cardIcon} />
              </View>
              <Typography variant="body" style={styles.cardLabel}>
                GoEats
              </Typography>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cardItem}
              onPress={() => navigation.navigate('GoTravel')}>
              <View style={styles.cardIconContainer}>
                <CImage source={Images.goTravel} style={styles.cardIcon} />
              </View>
              <Typography variant="body" style={styles.cardLabel}>
                GoTravel
              </Typography>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContainer}>
            {dummyArray.map((item, index) => (
              <CommunityCard
                variant="default"
                data={item}
                onMorePress={() => {}}
                onLikePress={() => {}}
                onCommentPress={() => {}}
                onSharePress={() => {}}
                showMore={false}
              />
            ))}
          </View>
        </ScrollView>
      </View>
    </ImageBackground>
  );
};

export default GoLife;
