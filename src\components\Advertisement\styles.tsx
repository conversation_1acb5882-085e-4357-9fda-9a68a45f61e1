import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
    },
    imageContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    image: {
      height: 71,
      width: 90,
      resizeMode: 'contain',
    },
    title: {
      color: theme.colors.text,
      marginVertical: 12,
    },
    subTitle: {
      color: theme.colors.text,
      marginHorizontal: 20,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 10,
      marginTop: 20,
      marginBottom: 20,
    },
    buttonTextPrimary: {
      color: theme.colors.text,
      fontSize: theme.fontSize.medium,
      fontWeight: '400',
    },
    buttonTextDark: {
      color: theme.colors.text,
      fontSize: theme.fontSize.medium,
      fontWeight: '400',
    },
    flex1: {
      flex: 1,
    },
    darkBtn: {
      borderWidth: 1,
    },
  });
