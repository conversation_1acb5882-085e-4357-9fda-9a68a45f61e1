import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';

interface SimpleSwipeBackProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
}

const SimpleSwipeBack: React.FC<SimpleSwipeBackProps> = ({
  children,
  enabled = true,
  threshold = 100,
}) => {
  const navigation = useNavigation();

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .failOffsetX([-Number.MAX_SAFE_INTEGER, -15])
    .failOffsetY([-20, 20])
    .onEnd(event => {
      // Check if swipe meets threshold and is going right
      const shouldGoBack = event.translationX > threshold && event.velocityX > 0;

      if (shouldGoBack && navigation.canGoBack()) {
        navigation.goBack();
      }
    });

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      <GestureDetector gesture={panGesture}>
        <View style={styles.content}>{children}</View>
      </GestureDetector>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default SimpleSwipeBack;
