import React from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import {PanGestureHandler, State} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';

interface SimpleSwipeBackProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
}

const {width: screenWidth} = Dimensions.get('window');

const SimpleSwipeBack: React.FC<SimpleSwipeBackProps> = ({
  children,
  enabled = true,
  threshold = 100,
}) => {
  const navigation = useNavigation();

  const onGestureEvent = (event: any) => {
    const {nativeEvent} = event;
    
    // Only handle right swipe (positive translationX)
    if (nativeEvent.state === State.END) {
      const {translationX, velocityX} = nativeEvent;
      
      // Check if swipe meets threshold and is going right
      const shouldGoBack = translationX > threshold && velocityX > 0;
      
      if (shouldGoBack && navigation.canGoBack()) {
        navigation.goBack();
      }
    }
  };

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      <PanGestureHandler
        onHandlerStateChange={onGestureEvent}
        activeOffsetX={[10, Number.MAX_SAFE_INTEGER]}
        failOffsetX={[-10, 0]}
        failOffsetY={[-20, 20]}
        shouldCancelWhenOutside={true}
        minPointers={1}
        maxPointers={1}>
        <View style={styles.content}>
          {children}
        </View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default SimpleSwipeBack;
