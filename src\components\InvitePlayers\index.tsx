import React, {useState, useEffect, useRef, useCallback} from 'react';
import {useThemeStore} from '@/store/themeStore';
import Tabs from '../Tabs';
import PlayerCard from '../PlayerCard';
import CButton from '../CButton';
import {styles as createStyles} from './styles';
import {BottomSheetFlatList, BottomSheetScrollView} from '@gorhom/bottom-sheet';
import Typography from '../Typography';
import useTranslation from '@/hooks/useTranslation';
import GroupCard from '../GroupCard';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {Controller, useForm} from 'react-hook-form';
import CInput from '../CInput';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CustomModal, Icon, RadioSelect} from '..';
import SearchInput from '../SearchInput';
import RatingSlider from '../RatingSlider';
import {invitePlayerTabs} from '@/config/staticData';

type Player = {
  id: string;
  name: string;
  rating: string;
  location: string;
  image: string;
  color?: string;
  isPremium?: boolean;
  status?: 'available' | 'busy' | 'in-match';
};

interface InviteFormData {
  name: string;
  email: string;
  phoneNumber: string;
  rating: number | null;
}

interface Group {
  id: number;
  name: string;
  members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
}

interface InvitePlayersProps {
  onConfirm?: (players: Player[]) => void;
  initialSelectedPlayers?: Player[];
}

const players: Player[] = [
  {
    id: '1',
    name: 'John Doe',
    rating: '3.5',
    location: 'Fort Greene',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
    status: 'available',
  },
  {
    id: '2',
    name: 'Jane Doe',
    rating: '3.5',
    location: 'Lincoln Terrace',
    image: 'https://randomuser.me/api/portraits/women/2.jpg',
    status: 'busy',
  },
  {
    id: '3',
    name: 'Babolat',
    rating: '3.5',
    location: 'McCarren Park',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    color: 'orange',
    isPremium: true,
    status: 'in-match',
  },
  {
    id: '4',
    name: 'John Doe',
    rating: '3.5',
    location: 'Fort Greene',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  {
    id: '5',
    name: 'Jane Doe',
    rating: '3.5',
    location: 'Lincoln Terrace',
    image: 'https://randomuser.me/api/portraits/women/2.jpg',
    isPremium: true,
  },
  {
    id: '6',
    name: 'Babolat',
    rating: '3.5',
    location: 'McCarren Park',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    color: 'orange',
  },
  {
    id: '7',
    name: 'John Doe',
    rating: '3.5',
    location: 'Fort Greene',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  {
    id: '8',
    name: 'Jane Doe',
    rating: '3.5',
    location: 'Lincoln Terrace',
    image: 'https://randomuser.me/api/portraits/women/2.jpg',
  },
  {
    id: '9',
    name: 'Babolat',
    rating: '3.5',
    location: 'McCarren Park',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    color: 'orange',
  },
];

const GroupsData: Group[] = [
  {
    id: 1,
    name: 'Sunday Clinic - Fort Greene',
    members: 7,
    highlighted: false,
    type: 'group',
    locked: true,
  },
  {id: 2, name: 'Tennis Bash', members: 5, highlighted: false, type: 'group', locked: true},
  {
    id: 3,
    name: 'Crown Heights Public Group',
    members: 7,
    highlighted: false,
    type: 'group',
    locked: true,
  },
];
const options = [
  {label: 'Tennis', value: 'tennis'},
  {label: 'Pickleball', value: 'pickleball'},
  {label: 'Plateform Tennis', value: 'plateform-tennis'},
  {label: 'Padel', value: 'padel'},
];
const options2 = [
  {label: 'Friends', value: 'friends'},
  {label: 'Invited', value: 'invited'},
  {label: 'Sponsored', value: 'sponsored'},
];

const InvitePlayers: React.FC<InvitePlayersProps> = ({onConfirm, initialSelectedPlayers = []}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState(t('invitePlayers.invitePlayerTabs.friends'));
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>(initialSelectedPlayers);
  const [selectedGroups, setSelectedGroups] = useState<Group[]>([]);
  const [isInviteModalVisible, setIsInviteModalVisible] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValue, setSelectedValue] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filterPlayers, setFilterPlayers] = useState<Player[]>(players);
  const [filterGroups, setFilterGroups] = useState<Group[]>(GroupsData);

  const [searchValueFilter, setSearchValueFilter] = useState('');
  const nameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneNumberInputRef = useRef<TextInput>(null);
  const ratingNumberInputRef = useRef<TextInput>(null);

  // Initialize with initial players data on mount
  useEffect(() => {
    if (initialSelectedPlayers && initialSelectedPlayers.length > 0) {
      setSelectedPlayers(initialSelectedPlayers);
    }
  }, [initialSelectedPlayers]);

  useEffect(() => {
    clearSearch();
  }, [activeTab]);

  const handlePlayerSelect = (player: Player) => {
    if (selectedPlayers.some(p => p.id === player.id)) {
      setSelectedPlayers(selectedPlayers.filter(p => p.id !== player.id));
    } else {
      setSelectedPlayers([...selectedPlayers, player]);
    }
  };

  const handleGroupSelect = (group: Group) => {
    if (selectedGroups.some(g => g.id === group.id)) {
      setSelectedGroups(selectedGroups.filter(g => g.id !== group.id));
    } else {
      setSelectedGroups([...selectedGroups, group]);
    }
  };

  const renderItem = ({item}: {item: any}) => {
    if (activeTab === t('invitePlayers.invitePlayerTabs.groups')) {
      return (
        <GroupCard
          name={item.name}
          members={item.members}
          highlighted={item.highlighted}
          locked={item.locked}
          containerStyle={{
            borderWidth: 1,
            borderColor: theme.colors.divider,
          }}
          onSelect={() => handleGroupSelect(item)}
          isSelected={selectedGroups.some(g => g.id === item.id)}
        />
      );
    } else {
      return (
        <PlayerCard
          key={item.id}
          playerData={item}
          onSelect={() => handlePlayerSelect(item)}
          isSelected={selectedPlayers.some(p => p.id === item.id)}
        />
      );
    }
  };

  const handleConfirm = () => {
    console.log('selectedPlayers selectedPlayers ===>', selectedPlayers);

    if (onConfirm) {
      onConfirm(selectedPlayers);
    }
  };

  const schema = yup.object().shape({
    name: yup.string().required('name is required'),
    email: yup.string().email('Email format is invalid').required('Email is required'),
    phoneNumber: yup.string().required('Phone number is required'),
    rating: yup.number().nullable().optional(),
  }) as yup.ObjectSchema<InviteFormData>;

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    watch,
  } = useForm<InviteFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      email: '',
      phoneNumber: '',
      rating: null,
    },
  });

  const onSubmit = () => {
    Keyboard.dismiss();
    setIsInviteModalVisible(true);
  };

  const clearSearch = () => setSearchQuery('');

  const handleSearch = useCallback(
    (query: string) => {
      if (activeTab === t('invitePlayers.invitePlayerTabs.groups')) {
        const filtered = query
          ? GroupsData.filter(
              group =>
                group.name.toLowerCase().includes(query.toLowerCase()) ||
                group.members.toString().toLowerCase().includes(query.toLowerCase()),
            )
          : GroupsData;

        setFilterGroups(filtered);
      } else {
        const filtered = query
          ? players.filter(
              player =>
                player.name.toLowerCase().includes(query.toLowerCase()) ||
                player.location.toLowerCase().includes(query.toLowerCase()),
            )
          : players;

        setFilterPlayers(filtered);
      }
    },
    [activeTab],
  );

  return (
    <>
      <Typography variant="title" style={styles.title} color={theme.colors.text}>
        {t('invitePlayers.invitePlayers')}
      </Typography>
      {activeTab !== t('invitePlayers.invitePlayerTabs.invite') && (
        <SearchInput
          placeholder={t('invitePlayers.searchNameLocationGroup')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={clearSearch}
          type="bottomSheetSearch"
          variant="light"
          onSearch={handleSearch}
          debounceTime={300}
          containerStyle={{marginBottom: 8}}
        />
      )}
      <Tabs
        tabs={invitePlayerTabs.map(item => t(item.title))}
        activeTab={activeTab}
        onTabPress={setActiveTab}
      />
      {activeTab === t('invitePlayers.invitePlayerTabs.friends') && (
        <View style={styles.titleContainer}>
          <Typography variant="parkTitle" color={theme.colors.text}>
            {activeTab}
          </Typography>

          <TouchableOpacity activeOpacity={0.7} onPress={() => setIsModalVisible(true)}>
            <Icon name="filter" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}

      {activeTab === t('invitePlayers.invitePlayerTabs.invite') ? (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}>
          <BottomSheetScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="name"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Name"
                      variant="dark"
                      showLabel={false}
                      placeholder="Enter a name"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.name}
                      error={errors.name?.message}
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={nameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Email address"
                      showLabel={false}
                      placeholder="Enter an email address"
                      variant="dark"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={[styles.input]}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => phoneNumberInputRef.current?.focus()}
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="phoneNumber"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="phoneNumber"
                      showLabel={false}
                      placeholder="Enter a phone number"
                      variant="dark"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.phoneNumber}
                      error={errors.phoneNumber?.message}
                      keyboardType="numeric"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={phoneNumberInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                      onSubmitEditing={() => ratingNumberInputRef.current?.focus()}
                      maxLength={10}
                    />
                  )}
                />
              </View>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="rating"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="rating"
                      showLabel={false}
                      placeholder="Enter rating (optional)"
                      variant="dark"
                      value={value?.toString() || ''}
                      onChangeText={text => onChange(text ? Number(text) : null)}
                      onBlur={onBlur}
                      hasError={!!errors.rating}
                      error={errors.rating?.message}
                      keyboardType="numeric"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={ratingNumberInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                      onSubmitEditing={() => handleSubmit(onSubmit)()}
                    />
                  )}
                />
              </View>
              <CButton
                title="Invite"
                variant="active"
                onPress={handleSubmit(onSubmit)}
                containerStyle={styles.inviteBtn}
                textStyle={styles.inviteBtnText}
              />
            </View>
          </BottomSheetScrollView>
        </KeyboardAvoidingView>
      ) : (
        <BottomSheetFlatList
          data={
            activeTab === t('invitePlayers.invitePlayerTabs.groups') ? filterGroups : filterPlayers
          }
          renderItem={renderItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={styles.scrollView}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          keyboardShouldPersistTaps="handled"
          style={styles.listContainer}
          onEndReached={() => console.log('Hello')}
        />
      )}
      {activeTab === t('invitePlayers.invitePlayerTabs.invite') ? null : (
        <CButton
          title={`${t('invitePlayers.accept')}${selectedPlayers.length > 0 ? `(${selectedPlayers.length})` : ''}`}
          onPress={handleConfirm}
          containerStyle={styles.buttonContainer}
        />
      )}
      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title={t('invitePlayers.filterResults')}>
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            Show
          </Typography>
          {options.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValue === option.value}
              onPress={() => setSelectedValue(option.value)}
            />
          ))}

          {/* Rating Slider */}
          <RatingSlider />

          {options2.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValue === option.value}
              onPress={() => setSelectedValue(option.value)}
            />
          ))}

          <SearchInput
            variant="light"
            placeholder="Search event"
            containerStyle={styles.filterInput}
            inputStyle={{color: theme.colors.primary}}
            value={searchValueFilter}
            placeholderTextColor={theme.colors.primary}
            onChangeText={setSearchValueFilter}
            iconColor={theme.colors.primary}
            onClear={() => setSearchValueFilter('')}
          />
          <CButton title="80 Results" onPress={() => setIsModalVisible(false)} variant="primary" />
        </View>
      </CustomModal>

      <CustomModal
        visible={isInviteModalVisible}
        onClose={() => setIsInviteModalVisible(false)}
        modalContainerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          minWidth: '85%',
          paddingHorizontal: 40,
          paddingTop: 40,
        }}>
        <View style={{alignItems: 'center', gap: 25, width: '100%'}}>
          <Icon name="arrow" size={150} color={theme.colors.activeColor} />
          <Typography variant="subtitle" style={{textAlign: 'center', color: theme.colors.text}}>
            {t('invitePlayers.inviteSent')}
          </Typography>
          <CButton
            title={t('invitePlayers.reserveGear')}
            onPress={() => setIsInviteModalVisible(false)}
            variant="pill"
            containerStyle={{width: '100%', backgroundColor: theme.colors.activeColor}}
            textStyle={{color: theme.colors.black}}
          />
          <CButton
            title={t('invitePlayers.close')}
            onPress={() => setIsInviteModalVisible(false)}
            variant="outline"
            containerStyle={{
              width: '100%',
              borderWidth: 0,
              borderColor: theme.colors.text,
            }}
          />
        </View>
      </CustomModal>
    </>
  );
};

export default InvitePlayers;
