import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {mmkvStorage} from '@/services/storage';
import {useConfigStore} from './configStore';

// Types
export interface User {
  name: string;
  email: string;
  birthyear?: string;
  profession?: string;
  primary_sport_reference?: string;
  location?: string;
  court_name?: string;
  years_playing?: string;
  skill_level?: string;
  favorite_brand?: string;
  memberships?: string;
  affiliations?: string;
}

// Auth state interface
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  isApiStatus: boolean;
  userLocation: {
    latitude: number | string;
    longitude: number | string;
  };
}

// Default auth values
export const defaultAuth: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
  isApiStatus: true,
  userLocation: {
    latitude: '',
    longitude: '',
  },
};

// Define the store type
type AuthStore = AuthState & {
  login: (user: object) => void;
  logout: () => void;
  loginStart: () => void;
  loginFailure: (error: string) => void;
  setUserLocation: (location: {latitude: number; longitude: number}) => void;
};

// Create the auth store with persistence
export const useAuthStore = create<AuthStore>()(
  persist(
    set => ({
      ...defaultAuth,

      login: user =>
        set({
          isAuthenticated: true,
          user: user as User,
          loading: false,
          error: null,
        }),

      setUserLocation: (location: {latitude: number; longitude: number}) =>
        set({
          userLocation: location,
        }),

      logout: () => {
        // Reset coach profile in config store
        useConfigStore.getState().setCoachProfile(false);
        useConfigStore.getState().setProfileSetup(false);
        useConfigStore.getState().setProfileStep('create_profile');

        set({
          ...defaultAuth,
        });
      },

      loginStart: () =>
        set({
          loading: true,
          error: null,
        }),

      loginFailure: error =>
        set({
          loading: false,
          error,
        }),
    }),
    {
      name: 'app-auth',
      storage: createJSONStorage(() => mmkvStorage),
    },
  ),
);
