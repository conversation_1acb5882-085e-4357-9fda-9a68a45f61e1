import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {SafeAreaView} from 'react-native-safe-area-context';
import withSwipeBack from '@/components/withSwipeBack';

const TestSwipeBackScreen = () => {
  const navigation = useNavigation();
  const theme = useThemeStore();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.white,
      textAlign: 'center',
      marginBottom: 30,
    },
    instruction: {
      fontSize: 16,
      color: theme.colors.white,
      textAlign: 'center',
      marginBottom: 20,
      lineHeight: 24,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 15,
      borderRadius: 8,
      marginTop: 20,
    },
    buttonText: {
      color: theme.colors.black,
      fontSize: 16,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    highlight: {
      color: theme.colors.primary,
      fontWeight: 'bold',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Swipe Back Gesture Test</Text>

      <Text style={styles.instruction}>
        This screen is designed to test the swipe back gesture functionality.
      </Text>

      <Text style={styles.instruction}>To test the swipe back gesture:</Text>

      <Text style={styles.instruction}>
        1. <Text style={styles.highlight}>Swipe from the left edge</Text> of the screen toward the
        right
      </Text>

      <Text style={styles.instruction}>
        2. Swipe from <Text style={styles.highlight}>anywhere on the screen</Text> toward the right
      </Text>

      <Text style={styles.instruction}>
        3. The screen should slide back to the previous screen with a smooth animation
      </Text>

      <Text style={styles.instruction}>
        This screen uses a <Text style={styles.highlight}>custom swipe back gesture</Text> that
        should work even if the native one doesn't.
      </Text>

      <TouchableOpacity style={styles.button} onPress={() => navigation.goBack()}>
        <Text style={styles.buttonText}>Go Back (Button)</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default withSwipeBack(TestSwipeBackScreen);
