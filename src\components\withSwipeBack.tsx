import React from 'react';
import SwipeBackGesture from './SwipeBackGesture';
import {useNavigation} from '@react-navigation/native';

interface WithSwipeBackOptions {
  enabled?: boolean;
  threshold?: number;
}

const withSwipeBack = <P extends object>(
  Component: React.ComponentType<P>,
  options: WithSwipeBackOptions = {}
) => {
  const WrappedComponent = (props: P) => {
    const navigation = useNavigation();
    const canGoBack = navigation.canGoBack();
    
    return (
      <SwipeBackGesture 
        enabled={canGoBack && (options.enabled !== false)}
        threshold={options.threshold}
      >
        <Component {...props} />
      </SwipeBackGesture>
    );
  };

  WrappedComponent.displayName = `withSwipeBack(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default withSwipeBack;
