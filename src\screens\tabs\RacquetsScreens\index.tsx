import React from 'react';
import {View, Dimensions, FlatList, ScrollView, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {CImage, Header, SafeAreaView} from '@/components';
import {ICarouselInstance, Pagination} from 'react-native-reanimated-carousel';
import Carousel from 'react-native-reanimated-carousel';
import {useSharedValue} from 'react-native-reanimated';
import {Images} from '@/config';
import BrandCarouselCard from '@/components/BrandCarouselCard';
import {servicesData, sportsData} from '@/config/staticData';
import SportsCard from '@/components/SportsCard';
import createStyles from './styles';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RacquetStackParamList} from '@/navigation/RacquetStack';
import Typography from '@/components/Typography';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<RacquetStackParamList>;

const RacquetsScreen = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const progress = useSharedValue<number>(0);
  const ref = React.useRef<ICarouselInstance>(null);
  const {t} = useTranslation();
  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      count: index - progress.value,
      animated: true,
    });
  };
  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };
  const data = [
    {
      brandName: 'Babolat',
      brandLogo: Images.babolat,
      image: Images.babolat,
    },
    {
      brandName: 'Head',
      brandLogo: Images.babolat,
      image: Images.babolat,
    },
    {
      brandName: 'Wilson',
      brandLogo: Images.babolat,
      image: Images.babolat,
    },
  ];

  const renderItem = ({item}: {item: OptionItem}) => {
    return (
      <SportsCard
        sportsTitle={item.sportsTitle}
        icon={item.icon}
        onCardPress={() => {
          navigation.navigate('RacquetCategory', {
            sportsTitle: item.sportsTitle,
          });
        }}
        containerStyle={styles.sportsCard}
      />
    );
  };

  const servicesRenderItem = ({item}: {item: OptionItem}) => {
    return (
      <SportsCard
        sportsTitle={item.sportsTitle}
        icon={item.icon}
        containerStyle={styles.sportsCard}
        onCardPress={() => {}}
      />
    );
  };

  return (
    <SafeAreaView
      includeBottom={false}
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[{name: 'cart', size: 24, color: theme.colors.activeColor}]}
        backgroundColor="transparent"
      />
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <View>
          <Typography variant="subTitle3" color={theme.colors.white}>
            {t('RacquetsScreen.topBrands')}
          </Typography>
          <View style={styles.topBrandContainer}>
            <CImage source={Images.topBrand} style={styles.topBrandImage} resizeMode="cover" />
          </View>
        </View>

        {/* Test Swipe Back Button */}
        <TouchableOpacity
          style={{
            backgroundColor: theme.colors.primary,
            padding: 15,
            borderRadius: 8,
            marginVertical: 20,
            marginHorizontal: 20,
          }}
          onPress={() => navigation.navigate('TestSwipeBack')}>
          <Typography
            variant="bodyMedium"
            color={theme.colors.black}
            style={{textAlign: 'center', fontWeight: 'bold'}}>
            Test Swipe Back Gesture
          </Typography>
        </TouchableOpacity>

        {/* All brans */}
        <View style={styles.allBrandContainer}>
          <Typography variant="subTitle3" color={theme.colors.white} style={styles.allBrandTitle}>
            {t('RacquetsScreen.allBrands')}
          </Typography>
          <Carousel
            ref={ref}
            width={Dimensions.get('screen').width}
            height={Dimensions.get('screen').height * 0.28}
            style={{alignSelf: 'center', marginVertical: 8, marginLeft: 31, overflow: 'visible'}}
            onProgressChange={progress}
            autoPlayInterval={3000}
            data={data}
            mode="parallax"
            modeConfig={{
              parallaxScrollingScale: 1,
              parallaxScrollingOffset: 70,
            }}
            panGestureHandlerProps={{
              activeOffsetX: [-10, 10],
            }}
            customConfig={() => ({
              type: 'positive',
            })}
            defaultIndex={0}
            snapEnabled={true}
            renderItem={({item}) => (
              <BrandCarouselCard
                brandName={item.brandName}
                brandLogo={item.brandLogo}
                image={item.image}
                style={{
                  width: Dimensions.get('screen').width * 0.75,
                }}
                onCardPress={() => {
                  navigation.navigate('RacquetBrands', {
                    sportsTitle: 'Tennis',
                    category: 'racquets',
                  });
                }}
              />
            )}
          />

          <Pagination.Basic
            progress={progress}
            data={data}
            dotStyle={{
              backgroundColor: 'transparent',
              height: 11,
              width: 26,
            }}
            activeDotStyle={{
              backgroundColor: theme.colors.orange1,
              height: 11,
              width: 26,
              borderRadius: 10,
            }}
            containerStyle={{
              backgroundColor: theme.colors.secondary,
              borderRadius: 10,
              gap: 0,
              padding: 0,
            }}
            onPress={onPressPagination}
          />
        </View>
        <View>
          <Typography variant="subTitle3" style={{color: theme.colors.text}}>
            {t('RacquetsScreen.sports')}
          </Typography>
          <FlatList
            data={sportsData}
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.sportsContent}
            horizontal
            showsHorizontalScrollIndicator={false}
          />
        </View>
        <View>
          <Typography variant="subTitle3" style={{color: theme.colors.text}}>
            {t('RacquetsScreen.services')}
          </Typography>
          <FlatList
            data={servicesData}
            renderItem={servicesRenderItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.sportsContent}
            horizontal
            showsHorizontalScrollIndicator={false}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default RacquetsScreen;
