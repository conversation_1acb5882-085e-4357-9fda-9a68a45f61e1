import React, {useState} from 'react';
import {View, FlatList, ImageBackground, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {CButton, Header, Icon, SafeAreaView} from '@/components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import createStyles from './styles';
import Typography from '@/components/Typography';
import PillLabel from '@/components/PillLabel';
import {Images} from '@/config';

interface OptionItem {
  id: string;
  label: string;
}

const FindCoach = () => {
  const theme = useThemeStore();
  const navigation = useNavigation<any>();
  const styles = createStyles(theme);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const toggleOption = (option: string) => {
    setError(null);
    setSelectedOptions(prev =>
      prev.includes(option) ? prev.filter(item => item !== option) : [...prev, option],
    );
  };

  const handleNext = () => {
    if (selectedOptions.length === 0) {
      setError('Please select at least one option');
      return;
    }

    navigation.navigate('CommunityHome', {
      type: 'playerConnect',
      data: {
        name: 'Jed',
        description: 'I am a tennis player',
        primaryCourts: 'Central Park Tennis, Manhattan, NY',
        avatar: Images.profile1,
      },
    });
  };

  const options: OptionItem[] = [
    {id: '1', label: 'Myself'},
    {id: '2', label: 'My Children'},
    {id: '3', label: 'Someone else'},
    {id: '4', label: 'Group'},
  ];

  const renderItem = ({item}: {item: OptionItem}) => {
    const isSelected = selectedOptions.includes(item.id);
    return (
      <PillLabel
        textStyle={{fontWeight: isSelected ? '700' : '300'}}
        label={item.label}
        backgroundColor={isSelected ? theme.colors.activeColor : theme.colors.dimGray}
        textColor={isSelected ? theme.colors.black : theme.colors.text}
        onPress={() => toggleOption(item.id)}
      />
    );
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles.container}>
      <SafeAreaView includeBottom={true} style={styles.container}>
        <Header
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          // pageTitle="GoFit"
          backgroundColor="transparent"
          isBackPress={true}
          leftIcon={{
            name: 'side-menu',
            size: 24,
            color: theme.colors.white,
            containerStyle: {backgroundColor: theme.colors.primary},
          }}
          leftIconButtonStyle={styles.menuButton}
          onLeftPress={openDrawer}
          title="Find a Coach"
          pageTitleStyle={{color: theme.colors.activeColor}}
        />
        <FlatList
          data={options}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.pillContainer}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          ListHeaderComponent={() => (
            <>
              <Typography variant="question" color={theme.colors.white} style={styles.title}>
                Who are these lessons for?
              </Typography>
              <Typography variant="bodyMedium" color={theme.colors.white} style={styles.subtitle}>
                Select all that apply
              </Typography>
            </>
          )}
        />
        {error && (
          <Typography variant="errorText" color={theme.colors.coralRed} style={styles.errorText}>
            {error}
          </Typography>
        )}
        <View style={styles.buttonContainer}>
          <CButton
            title="Next"
            variant="active"
            textStyle={{color: theme.colors.black}}
            containerStyle={{minWidth: 200, paddingVertical: 10}}
            onPress={handleNext}
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default FindCoach;
