{"//": "Traducciones en Español para la aplicación (spanish)", "common": {"ok": "OK", "cancel": "<PERSON><PERSON><PERSON>", "back": "Atrás", "next": "Siguient<PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "loading": "Cargando...", "error": "Error", "success": "Éxito", "search": "Buscar", "SEARCH": "BUSCAR", "community": "COMUNIDAD", "kiosk": "KIOSCO", "filter": "Filtrar", "apply": "Aplicar", "reset": "Restablecer", "close": "<PERSON><PERSON><PERSON>", "upload": "Subir", "invite": "Invitar", "uploadContacts": "Subir contactos", "enterName": "Ingresa un nombre", "enterEmail": "Ingresa una dirección de correo electrónico", "enterPhone": "Ingresa un número de teléfono", "enterAtpRating": "Ingresa clasificación ATP (Opcional)", "on": "ACTIVADO", "off": "DESACTIVADO", "create": "<PERSON><PERSON><PERSON>", "public": "Público", "private": "Privado", "hidden": "Oculto", "skip": "<PERSON><PERSON><PERSON>", "age": "Edad", "reportAbuse": "<PERSON>un<PERSON>r abuso", "inviteMore": "¡Invitar a más!", "inviteSent": "¡La invitación ha sido enviada!"}, "auth": {"login": "In<PERSON><PERSON>", "signup": "Registrarse", "email": "Correo electrónico", "password": "Contraseña", "forgotPassword": "¿Olvidaste tu contraseña?", "loginWithEmail": "Iniciar sesión con correo electrónico", "loginWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "loginWithFacebook": "Iniciar se<PERSON><PERSON> con Facebook", "dontHaveAccount": "¿No tienes una cuenta?", "alreadyHaveAccount": "¿Ya tienes una cuenta?", "createAccount": "<PERSON><PERSON><PERSON> cuenta", "logout": "<PERSON><PERSON><PERSON>", "verification": "Verificación"}, "profile": {"profile": "Perfil", "settings": "Configuración", "personalInfo": "Información personal", "name": "Nombre", "phoneNumber": "Número de teléfono", "bio": "Biografía", "updateProfile": "Actualizar perfil", "createProfile": "<PERSON><PERSON><PERSON> perfil", "whichBestDescribesYou": "¿C<PERSON>ál te describe mejor?"}, "settings": {"settings": "Configuración", "language": "Idioma", "notifications": "Notificaciones", "darkMode": "<PERSON><PERSON> oscuro", "biometrics": "Biometría", "termsOfService": "Términos de servicio", "privacyPolicy": "Política de privacidad", "shareLocationWithFriends": "Compartir ubicación con amigos", "twoFactorAuthentication": "Autenticación de dos factores", "about": "Acerca de", "version": "Versión", "searchSettings": "Configuración de búsqueda"}, "booking": {"searchParks": "Buscar parques", "selectDateTime": "Seleccionar fecha y hora", "purchaseEquipments": "Comprar equipamiento", "cartView": "<PERSON><PERSON>", "createProfile": "<PERSON><PERSON><PERSON> perfil", "signup": "Registrarse", "advertisement": "Publicidad", "subscription": "Suscripción"}, "parks": {"findParks": "Encontrar parques", "nearbyParks": "Parques cercanos", "location": "Ubicación", "distance": "Distancia", "facilities": "Instalaciones", "courts": "<PERSON><PERSON><PERSON>", "availableNow": "Disponible ahora"}, "equipment": {"equipment": "Equipamiento", "racquets": "RAQUETAS", "balls": "PELOTAS", "accessories": "Accesorios", "brand": "<PERSON><PERSON>", "price": "Precio", "addToCart": "Agregar al carrito", "removeFromCart": "Quitar del carrito"}, "permissions": {"locationPermissions": "Permisos de ubicación", "locationDescription": "Esta aplicación requiere servicios de ubicación", "enableLocation": "Habilitar ubicación", "enableBluetooth": "Habilitar Bluetooth", "enableNotifications": "Habilitar notificaciones", "noThanks": "No, gracias", "bluetoothPermissions": "<PERSON><PERSON><PERSON> de Bluetooth", "bluetoothDescription": "Funciona mejor cuando Bluetooth está habilitado", "notificationPermissions": "Permisos de notificaciones", "notificationDescription": "Funciona mejor cuando las notificaciones están habilitadas"}, "terms": {"title": "Términos y condiciones", "accept": "Aceptar"}, "signupScreen": {"signUpWith": "Registrarse con", "facebook": "Facebook", "google": "Google", "email": "Correo electrónico", "alreadyHaveAnAccount": "¿Ya tienes una cuenta?", "login": "In<PERSON><PERSON>", "bySigningUpYouAgreeToOur": "Al registrarte aceptas nuestros", "termsAndConditions": "Términos y condiciones", "signin": "In<PERSON><PERSON>"}, "login": {"title": "In<PERSON><PERSON>", "enterYourEmail": "Ingresa tu correo electrónico", "emailAddress": "Dirección de correo electrónico", "enterYourPassword": "Ingresa tu contraseña", "password": "Contraseña", "forgotYourPassword": "¿Olvidaste tu contraseña?", "signUp": "Registrarse", "submit": "Siguient<PERSON>", "dontHaveAccount": "¿No tienes una cuenta?"}, "createProfile": {"createProfileTitle": "Crea tu perfil de Goraqt", "name": "Nombre para mostrar", "namePlaceholder": "Ingresa nombre para mostrar", "nameError": "El nombre es obligatorio", "email": "Correo electrónico", "emailPlaceholder": "Ingresa una dirección de correo electrónico", "emailError": "El correo electrónico es obligatorio", "emailFormatError": "El formato del correo electrónico es inválido", "randomNameGenerator": "Nombres para mostrar generados", "age": "Edad", "agePlaceholder": "Ingresa edad", "ageError": "La edad es obligatoria", "ageNumberError": "La edad debe ser un número", "ageLengthError": "La edad debe estar entre 13 y 120", "ageFormatError": "El formato de edad es inválido", "userTypeError": "Por favor selecciona una opción", "fitnessLevelError": "Por favor selecciona un nivel de condición física", "describe": "Descríbete", "describeFitness": "¿Cuál describe mejor tu nivel de condición física/habilidad?", "submitBtn": "Completar perfil", "setupLaterBtn": "Configurar des<PERSON>", "birthYear": "Año de nacimiento", "birthYearPlaceholder": "Ingresa año de nacimiento", "birthYearformatErr": "<PERSON>be ser un año válido (AAAA)", "birthYearRangeErr": "El año de nacimiento no puede ser mayor al año actual y no debe ser más de 60 años atrás", "birthYearRequired": "El año de nacimiento es obligatorio"}, "advertisement": {"title": "Prueba 30 días de raquetas Wilson por $25", "subTitle": "Obtendrás 1 hora de juego por día durante 30 días, 50% de descuento en una lata de pelotas Wilson y un crédito de $20 para la compra de raquetas", "skipBtn": "<PERSON><PERSON><PERSON>", "claimOfferBtn": "<PERSON><PERSON><PERSON><PERSON>"}, "subscription": {"trial": "Prueba de 30 días", "price": "$20.44", "promoMessage": "Después de tu período de prueba gratuita, obtén 10% de descuento en la membresía con una tarjeta de crédito o débito American Express elegible.", "bulletTitle": "Punto de promoción", "bulletSubtitle": "adipiscing elit, sed do eiusmod tempor", "promoCodePlaceholder": "INGRESA CÓDIGO PROMOCIONAL", "termsText": "Al comprar Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud", "termsLink": "Sujeto a términos y condiciones", "acceptBtn": "Aceptar"}, "createProfileLogin": {"title": "<PERSON><PERSON>", "subTitle": "Solo necesitamos algunos detalles más para configurarte.", "age": "¿Cuántos años tienes?", "agePlaceholder": "Ingresa tu edad", "ageRequired": "La edad es obligatoria", "ageNumber": "La edad debe ser un número", "ageRange": "La edad debe estar entre 13 y 120", "userTypeRequired": "Por favor selecciona una opción", "fitnessLevelRequired": "Por favor selecciona un nivel de condición física", "userType": "¿C<PERSON>ál te describe mejor?", "fitnessLevel": "¿Cuál describe mejor tu nivel de condición física/habilidad?", "completeProfile": "Completar perfil", "setupLater": "Configurar des<PERSON>", "birthYear": "Año de nacimiento", "birthYearPlaceholder": "Ingresa año de nacimiento", "birthYearformatErr": "<PERSON>be ser un año válido (AAAA)"}, "ParksSearchScreen": {"niceToSeeYou": "<PERSON>s bueno verte", "guest": "<PERSON><PERSON><PERSON><PERSON>", "whereDoYouWantToPlay": "¿<PERSON><PERSON>de quieres jugar?", "noParksFound": "No se encontraron parques", "loadingMoreParks": "Cargando más parques...", "noKiosk": "No hay un kiosco inteligente en esta área.", "recommendLocation": "Recomendar una ubicación", "enter": "Por favor ingresa", "address": "Dirección", "city": "Ciudad", "state": "Estado", "submit": "Enviar", "zipCode": "Código postal", "nameRequired": "el nombre es obligatorio", "nameInvalid": "El nombre solo puede contener letras", "addressRequired": "la dirección es obligatoria", "cityRequired": "la ciudad es obligatoria", "cityInvalid": "La ciudad solo puede contener letras", "stateRequired": "el estado es obligatorio", "stateInvalid": "El estado solo puede contener letras", "zipCodeRequired": "el código postal es obligatorio", "invalidZipCode": "Código postal inválido"}, "dateScreen": {"selectDateAndTime": "Selecciona una fecha y hora"}, "datePicker": {"selectDateAndTime": "Selecciona una fecha y hora"}, "customDatePicker": {"addPlayers": "Agregar jugadores", "otherPlayers": "<PERSON><PERSON>s jugador<PERSON>"}, "invitePlayers": {"invitePlayers": "Invitar jugador<PERSON>", "accept": "Aceptar", "friends": "Amigos", "groups": "Grupos", "nearby": "Cercano", "invite,": "Invitar", "searchNameLocationGroup": "Buscar nombre, ubicación, grupo, etc.", "filterResults": "Filtrar resultados", "reserveGear": "Reservar equipamiento", "inviteSent": "¡Invitación enviada!", "close": "<PERSON><PERSON><PERSON>", "invitePlayerTabs": {"friends": "Amigos", "groups": "Grupos", "nearby": "Cercano", "invite": "Invitar"}}, "ShoppingCartSection": {"yourCart": "Tu carrito", "total": "Total", "proceedToCheckout": "Proceder al pago", "emptyCart": "Tu carrito está vacío", "findAPark": "Encontrar un parque"}, "EquipmentReservation": {"filterResults": "Filtrar resultados", "availableNowInKiosk": "Disponible ahora en el kiosco", "age": "Edad", "apply": "Aplicar", "previous": "Anterior", "next": "Siguient<PERSON>", "reserveOrPurchaseEquipment": "Reservar o comprar equipamiento"}, "EquipmentDetailModal": {"tryItNow": "¡PRUÉBALO AHORA!", "specifications": "Especificaciones", "averageRating": "Calificación promedio", "playerReviews": "Reseñas de jugadores", "more": "Más", "less": "<PERSON><PERSON>", "staffReviews": "Reseñas del personal"}, "RacquetsScreen": {"topBrands": "Marcas principales", "allBrands": "Todas las marcas", "sports": "Deportes", "services": "<PERSON><PERSON><PERSON>"}, "RacquetCategoryScreen": {"byBrand": "Por marca", "bestForMyGame": "Mejor para mi juego"}, "RacquetsBrandsScreen": {"brands": "<PERSON><PERSON>"}, "RacquetSelector": {"racquetSelector": "<PERSON><PERSON> <PERSON>", "howLongHaveYouBeenPlayingTennis": "¿Por cuánto tiempo has\nestado jugando tenis?", "lessThan2Years": "0-2 a<PERSON><PERSON>", "moreThan2Years": "2+ a<PERSON>s", "whichBestDescribesYourFitnessLevel": "¿Cuál describe mejor\ntu nivel de condición física?", "slowAndSteadyOnTheTreadmill": "Lento y constante\nen la caminadora", "workoutWarrior": "Guerrero del\nejercicio", "whichCharacteristicsDoYouMostWantInARacquet": "¿Qué características deseas\nmás en una raqueta?", "comfortableAndArmFriendly": "Cómoda y\namigable con el brazo", "lotsOfPower": "<PERSON><PERSON>\npotencia"}, "RacquetSelectorDetail": {"RacquetSelectorDetail": "<PERSON><PERSON> <PERSON>", "tryItNow": "¡PRUÉBALO AHORA!", "results": "Resul<PERSON><PERSON>"}, "CartScreen": {"Subscriber": "Suscriptor", "cart": "<PERSON><PERSON>", "description": "Después de tu período de prueba gratuita, obtén 10% de descuento en la membresía con una tarjeta de crédito o débito American Express elegible.", "total": "Total", "continueShopping": "Continuar comprando", "reserve": "<PERSON><PERSON><PERSON>", "youAreAllSetToPlayNow": "¡Ya estás listo para jugar! Camina al kiosco más cercano y escanea el código QR para comenzar a jugar"}, "BottomSheetComp": {"niceToSeeYou": "<PERSON>s bueno verte", "Guest": "<PERSON><PERSON><PERSON><PERSON>"}, "drawer": {"notifications": "Notificaciones", "referFriend": "Referir un amigo", "myMatches": "Mis partidos", "rewards": "Recompensas", "recycleBalls": "Reciclar pelotas", "help": "<PERSON><PERSON><PERSON>", "settings": "Configuración", "manageCommunity": "Administrar comunidad", "orders": "Pedidos", "assistantCoach": "Entrenador asistente"}, "assistantCoach": {"welcome": "Bienvenido al entrenador asistente", "subtitle": "Tu fuente única para la gestión gratuita de deportes de raqueta"}, "InvitePlayersAssistantCoach": {"title": "Invita a tus jugadores", "playerName": "Nombre del jugador", "playerPhone": "Número de teléfono del jugador", "playerEmail": "Dirección de correo electrónico del jugador", "name": "Ingresa nombre", "phone": "Ingresa número de teléfono", "email": "Ingresa dirección de correo electrónico"}, "assistantCoachOptions": {"editProfile": "Editar perfil de entrenamiento", "calendar": "Calendario", "manageClasses": "Administrar clases", "managePlayerAssets": "Administrar recursos de jugadores", "manageServiceRequest": "Administrar solicitud de servicio", "manageContent": "Administrar contenido", "postAnAd": "Publicar un anuncio", "orderReserveEquipment": "Pedir/Reservar equipamiento", "getCertified": "Obtener certificación", "messageNotificationPreferences": "Preferencias de mensajes y notificaciones", "invitePlayers": "Invita a tus jugadores", "title": "Entrenador asistente"}, "drawerNotification": {"notifications": "Notificaciones", "searchNotifications": "Buscar notificaciones", "notificationsWidget": "Widget de notificaciones", "chatWidget": "Widget de chat", "inviteFriendsGet10Off": "Invita amigos, obtén 10% de descuento"}, "drawerReferFriend": {"referFriend": "Referir un amigo", "searchReferrals": "Buscar referencias", "inviteFriendsGet10Off": "Invita amigos, obtén 10% de descuento", "uploadContacts": "Subir contactos"}, "drawerMyMatches": {"myMatches": "Mis partidos", "searchMatches": "Buscar partidos", "calendar": "Calendario", "matchHistory": "Historial de partidos", "notificationsWidget": "Widget de notificaciones", "chatWidget": "Widget de chat", "noMatchesFound": "No se encontraron partidos"}, "balls": {"purchase": "Comprar pelotas", "search": "Buscar pelotas", "help": "Ayúdame a elegir una pelota de tenis", "filter": "Filtrar resultados", "availableNowInKiosk": "Disponible ahora en el kiosco", "age": "Edad"}, "shareScreen": {"needHelp": "¿Necesitas ayuda?", "pickup": "Recoger / Devolver", "direct": "Conexión directa"}, "CommunityScreen": {"home": "<PERSON><PERSON>o", "playerConnect": "Conectar jugador", "reviews": "Reseñas", "groups": "Grupos", "goLife": "GoLife", "upYourGame": "UpYourGame", "goStream": "GoStream"}, "editCoachProfile": {"title": "Editar perfil de entrenamiento", "addPhoto": "AGREGAR FOTO", "description": "Descripción del perfil", "descriptionPlaceholder": "Ingresa descripción", "descriptionRequired": "La descripción es obligatoria", "certifications": "Certificaciones", "private": "Privado", "locationsAvailable": "Ubicaciones disponibles", "publicCourts": "Canchas p<PERSON>", "sportsClub": "Clubes deportivos", "videoCoaching": "Entrenamiento por video", "rate": "<PERSON><PERSON><PERSON>", "otherServices": "<PERSON><PERSON><PERSON> servic<PERSON>", "stringing": "Encordado", "gripping": "Empuñadura", "customization": "Personalización", "CoachingSkillsSets": "Conjuntos de habilidades de entrenamiento", "doubles": "<PERSON><PERSON>", "footwork": "Juego de pies y acondicionamiento", "funGames": "Juegos divertidos", "groupLessons": "Lecciones grupales", "mentalSkills": "Habilidades mentales", "privateLessons": "Lecciones privadas", "redOrange": "Progresión de pelotas rojas, naranjas y verdes", "singles": "Singles", "technicalFundamentals": "Fundamentos técnicos", "typesOfPlayers": "Tipos de jugadores", "adult": "Adulto", "learning": "Aprendiendo a jugar", "middleSchool": "Escuela media", "preSchool": "<PERSON>es<PERSON>r y jardín de infantes", "socialPlayer": "Jugador social", "youth": "Juvenil", "save": "Guardar"}, "calendarScreen": {"title": "Calendario", "newClass": "Nueva clase", "manageClasses": "Administrar clases", "upcomingClasses": "Próximas clases", "today": "Hoy", "upcoming": "Próximo", "widget": "Widget"}, "manageClassesScreen": {"title": "Administrar clases", "unscheduled": "Sin programar", "className": "Nombre de la clase", "classNamePlaceholder": "Ingresa nombre", "classDescription": "Descripción de la clase", "classDescriptionPlaceholder": "Ingresa descripción", "classDescriptionRequired": "La descripción es obligatoria", "selectDate": "Seleccionar fecha, hora y frecuencia", "maximumStudents": "Número máximo de estudiantes", "numberPlaceholder": "Ingresa número", "private": "Privado", "scheduledClasses": "Clases programadas"}, "managePlayerAssetsScreen": {"title": "Administrar recursos de jugadores", "students": "Estudiantes"}, "manageServiceRequestScreen": {"title": "Administrar solicitud de servicio", "stringing": "Encordado", "grips": "Empuñadu<PERSON>", "customs": "Personalizaciones"}, "manageContentScreen": {"title": "Administrar contenido", "storage": "Almacenamiento", "upgrade": "Actualizar", "stats": "Estadísticas", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "videos": "Videos", "today": "Hoy", "yesterday": "Ayer"}, "postAnAdScreen": {"title": "Publicar un anuncio", "classListing": "Listado de clases", "saveDraft": "Guardar borrador", "className": "Nombre de la clase", "classNamePlaceholder": "Ingresa nombre", "classDescription": "Descripción de la clase", "classDescriptionPlaceholder": "Ingresa descripción", "classDescriptionRequired": "La descripción es obligatoria", "selectDate": "Seleccionar fecha, hora y frecuencia", "maximumStudents": "Número máximo de estudiantes", "numberPlaceholder": "Ingresa número", "preview": "Vista previa", "post": "Publicar", "classRequired": "El nombre de la clase es obligatorio"}, "orderReserveEquipmentScreen": {"title": "Pedir/Reservar equipamiento", "subTitle": "Reservar o comprar equipamiento"}, "getCertifiedScreen": {"title": "Obtener certificación", "recCoachWorkshop": "Taller de entrenador recreativo", "level1": "Nivel 1", "level2": "Nivel 2", "level3": "Nivel 3", "specialtyWorkshops": "Talleres especializados", "ptrw": "PTRW"}, "messageNotificationPreferencesScreen": {"title": "Preferencias de mensajes y notificaciones", "pushNotifications": "Notificaciones push"}, "communityTabs": {"home": "<PERSON><PERSON>o", "playerConnect": "Conectar jugador", "reviews": "Reseñas", "groups": "Grupos", "goLife": "GoLife", "upYourGame": "UpYourGame", "goStream": "GoStream"}, "findPlayer": {"searchPlaceHolder": "Buscar nombre, ubicación, grupo, etc.", "accept": "Aceptar", "findPlayerTabs": {"schedulePlay": "Programar juego", "nearby": "Cercano", "friends": "Amigos", "group": "Grupo", "invite": "Invitar"}, "title": "Buscar jugadores", "invitePlayers": "Invitar jugador<PERSON>", "namePlaceholder": "Ingresa un nombre", "emailPlaceholder": "Ingresa una dirección de correo electrónico", "phonePlaceholder": "Ingresa un número de teléfono", "ratingPlaceholder": "Ingresa una calificación (opcional)", "nameRequired": "El nombre es obligatorio", "emailInvalid": "El formato de correo electrónico es inválido", "phoneRequired": "El número de teléfono es obligatorio", "name": "Nombre", "email": "Correo electrónico", "phoneNumber": "Número de teléfono", "rating": "Calificación", "searchEvent": "Buscar evento", "Results": "Resul<PERSON><PERSON>"}, "groupsScreen": {"title": "Grupos", "createGroup": "Crear grupo", "myGroups": "Mis grupos", "joinGroup": "Unirse al grupo", "scoreBoard": "Marcador", "allGroups": "Todos los grupos", "favoriteGroups": "Grupos favoritos"}, "newGroupScreen": {"title": "Crear grupo", "newGroup": "Nuevo grupo", "groupNamePlaceholder": "Ingresa nombre del grupo", "groupSettings": "Configuración del grupo", "privacy": "Privacidad", "favoriteLocation": "Ubicación favorita para jugar", "favoriteLocationPlaceholder": "Ingresa favorito", "tags": "Etiquetas"}, "addMembersScreen": {"title": "Agregar miembros", "search": "Buscar", "addMembers": "Agregar miembros", "searchPlaceholder": "Buscar nombre o correo electrónico", "results": "Resul<PERSON><PERSON>", "filterResults": "Filtrar resultados", "show": "Mostrar", "tennis": "<PERSON><PERSON>", "pickleball": "Pickleball", "padel": "<PERSON><PERSON><PERSON>", "platformTennis": "<PERSON><PERSON>", "utrRating": "Clasificación UTR", "friends": "Amigos", "invited": "<PERSON><PERSON><PERSON><PERSON>", "sponsored": "Patrocinado", "searchEvent": "Buscar evento"}, "createGroupMemberListScreen": {"title": "Crear grupo", "newGroup": "Nuevo grupo", "groupNamePlaceholder": "Ingresa nombre del grupo", "groupSettings": "Configuración del grupo", "members": "Mi<PERSON><PERSON><PERSON>", "of": "de"}, "myGroupsScreen": {"title": "Mis grupos"}, "joinGroupsScreen": {"title": "Unirse al grupo", "searchPlaceholder": "Buscar nombre o correo electrónico"}, "joinGroupDetailsScreen": {"title": "Unirse al grupo", "joinGroup": "Unirse al grupo"}, "reviewsScreen": {"title": "Últimas Reseñas", "refineResults": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Buscar Reseñas", "results": "Resul<PERSON><PERSON>", "noReviewsFound": "No se Encontraron Reseñas", "noReviewsFoundMessage": "No hay reseñas disponibles en este momento"}, "commentScreen": {"title": "Escribir un comentario", "rating": "Calificación", "headline": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descripción", "addImage": "Agregar <PERSON>n", "ratingRequired": "La calificación es obligatoria", "selectRating": "Por favor seleccione una calificación", "headlineRequired": "El título es obligatorio", "descriptionRequired": "La descripción es obligatoria", "maxCharacters": "Máx. 1500 caracteres", "uploadImagesAndVideos": "Subir imágenes y videos", "writeHeadline": "Escribir un Título", "describeExperience": "Describe tu experiencia con este equipo. Incluye qué estuvo bien y por qué, á<PERSON>s de mejora, si las hay. Límite de 1500 caracteres"}, "BiometricsScreen": {"title": "Autenticación biométrica", "notAvailable": "La autenticación biométrica no está disponible en su dispositivo.", "description": "Use {getBiometryTypeText()} para proteger su aplicación y evitar ingresar su contraseña cada vez que abra la aplicación.", "biometrics": "Biometría", "faceId": "Face ID", "touchId": "Touch ID", "enable": "Habilitar", "whenEnabled": "<PERSON>uando esté habilitado,", "required": " será requerido cada vez que abra la aplicación.", "protection": "Protección", "use": "<PERSON>ar", "toSecure": "para proteger su aplicación y evitar ingresar su contraseña cada vez que abra la aplicación.", "biometricAuthenticationEnabled": "La autenticación biométrica ha sido habilitada", "failedToEnableBiometricAuthentication": "Error al habilitar la autenticación biométrica", "biometricAuthenticationDisabled": "La autenticación biométrica ha sido deshabilitada", "failedToDisableBiometricAuthentication": "Error al deshabilitar la autenticación biométrica", "errorOccurredWhileUpdatingBiometricSettings": "Ocurrió un error al actualizar la configuración biométrica"}, "NotificationScreen": {"title": "Notificaciones", "notifications": "Notificaciones", "searchNotifications": "Buscar notificaciones", "notificationsWidget": "Widget de notificaciones", "chatWidget": "Widget de chat", "trash": "Papelera", "more": "Más", "notificationFunction": "Función de notificación", "reply": "<PERSON><PERSON><PERSON><PERSON>", "remindMe": "Recordarme"}, "equipmentReservationScreen": {"title": "Reservar o comprar equipamiento", "age": "Edad", "availableNowInKiosk": "Disponible ahora en el kiosco"}}