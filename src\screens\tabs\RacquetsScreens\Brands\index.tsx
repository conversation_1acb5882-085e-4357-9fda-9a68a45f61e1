import React from 'react';
import {Dimensions, ScrollView} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {<PERSON>er, SafeAreaView} from '@/components';
import Carousel, {ICarouselInstance, Pagination} from 'react-native-reanimated-carousel';
import {useSharedValue} from 'react-native-reanimated';
import {Images} from '@/config';
import BrandCarouselCard from '@/components/BrandCarouselCard';
import createStyles from './styles';
import {NativeStackNavigationProp, NativeStackScreenProps} from '@react-navigation/native-stack';
import {RacquetStackParamList} from '@/navigation/RacquetStack';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<RacquetStackParamList>;
type Props = NativeStackScreenProps<RacquetStackParamList, 'RacquetBrands'>;

const RacquetsBrandsScreen = ({route}: Props) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const {sportsTitle} = route.params;
  const {t} = useTranslation();
  const progress = useSharedValue<number>(0);
  const ref = React.useRef<ICarouselInstance>(null);
  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      count: index - progress.value,
      animated: true,
    });
  };
  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };
  const data = [
    {
      brandName: 'Babolat',
      brandLogo: Images.tennisBrand,
      image: Images.babolat,
    },
    {
      brandName: 'Head',
      brandLogo: Images.tennisBrand,
      image: Images.babolat,
    },
    {
      brandName: 'Wilson',
      brandLogo: Images.tennisBrand,
      image: Images.babolat,
    },
  ];

  return (
    <SafeAreaView
      includeBottom={false}
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[{name: 'cart', size: 24, color: theme.colors.activeColor}]}
        backgroundColor="transparent"
        pageTitle={`${sportsTitle} ${t('RacquetsBrandsScreen.brands')}`}
      />
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <Carousel
          ref={ref}
          width={Dimensions.get('screen').width}
          style={styles.carousel}
          onProgressChange={progress}
          data={data}
          mode="parallax"
          modeConfig={{
            parallaxScrollingScale: 1,
            parallaxScrollingOffset: 75,
          }}
          defaultIndex={0}
          snapEnabled={true}
          renderItem={({item}) => (
            <BrandCarouselCard
              brandName={item.brandName}
              brandLogo={item.brandLogo}
              image={item.image}
              style={styles.card}
              onCardPress={() => navigation.navigate('RacquetSelectorDetail')}
            />
          )}
        />

        <Pagination.Basic
          progress={progress}
          data={data}
          dotStyle={styles.dot}
          activeDotStyle={styles.activeDot}
          containerStyle={styles.pagination}
          onPress={onPressPagination}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default RacquetsBrandsScreen;
