import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {SearchScreen} from '@/screens/tabs';
import SearchBottomSheet from '@/components/SearchBottomSheet/SearchBottomSheet';

export type SearchStackParamList = {
  SearchScreen: undefined;
  DateScreen: undefined;
  EquipmentReservationScreen: undefined;
};

const Stack = createNativeStackNavigator<SearchStackParamList>();
const SearchScreenWithBottomSheet = () => (
  <>
    <SearchScreen />
    <SearchBottomSheet />
  </>
);

const SearchStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="SearchScreen"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true, // Enable gesture navigation
        gestureDirection: 'horizontal', // iOS style (default)
        gestureResponseDistance: {start: 50, end: -1}, // Distance from edge to trigger gesture
      }}>
      <Stack.Screen
        name="SearchScreen"
        component={SearchScreenWithBottomSheet}
        options={{animation: 'slide_from_right'}}
      />
    </Stack.Navigator>
  );
};

export default SearchStack;
