import React, {useState} from 'react';
import {View, TouchableOpacity, ScrollView, Text, StyleSheet} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {Header, Icon, SafeAreaView} from '@/components';
import {OfferBanner} from '@/components/common/OfferBanner';
import SearchInput from '@/components/SearchInput';
import {styles as createStyles} from './styles';
import Typography from '@/components/Typography';

const HelpScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();

  const styles = createStyles(theme);

  const [searchValue, setSearchValue] = useState('');

  const helpTopics = [
    'Search bar',
    'Notifications (push, etc.)',
    'Messages',
    'Two factor Authentication',
    'Share location to friends',
    'Security and Logins',
    'Account Recovery',
    'Location Services',
    'Privacy (public, private)',
    'Privacy Policy Legal Regulations',
    'Visibility',
    'Blocked Users',
    'Requests Control',
    'About (app version)',
    'Font Size',
    '3rd party App integrations',
    'Logout',
    'Footer Nav Bar',
  ];

  const filteredTopics = helpTopics.filter(topic =>
    topic.toLowerCase().includes(searchValue.toLowerCase()),
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle="Help"
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />
      <View style={styles.searchContainer}>
        <SearchInput placeholder="Search Help" value={searchValue} onChangeText={setSearchValue} />
      </View>
      <ScrollView style={styles.topicsList} contentContainerStyle={null}>
        {filteredTopics.map((topic, idx) => (
          <Typography variant="description" key={topic} color={theme.colors.white}>
            {topic}
          </Typography>
        ))}
      </ScrollView>
      <View style={styles.footer}>
        <OfferBanner text="Invite friends, get 10% off" />
      </View>
    </SafeAreaView>
  );
};

export default HelpScreen;
