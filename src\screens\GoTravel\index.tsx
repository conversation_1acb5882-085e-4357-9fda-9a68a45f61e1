import React, {useState} from 'react';
import {FlatList, View, TouchableOpacity, ImageBackground, RefreshControl} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Header, Icon, NoData, SafeAreaView} from '@/components';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Tabs from '@/components/Tabs';
import CommunityCard from '@/components/CommunityCard';
import Typography from '@/components/Typography';
import {Images} from '@/config';
import MediaCard from '@/components/MediaCard';
import {getCommunityData, setupCommunityMutation} from '@/utils/commonFunctions';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import VideoActionsModal from '@/components/VideoActionsModal';
import CLoader from '@/components/CLoader';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

interface GoTravelData {
  id: string;
  title: string;
  description: string;
  image: string;
  file_thumbnail: string;
  tags: GoTravelTag[];
  buttonText?: string;
  is_liked: boolean;
  likes_count: number;
  [key: string]: any;
}

interface GoTravelTag {
  id: number;
  name: string;
  title: string;
}

type ApiResponse = GoTravelData[];

interface VideoModalState {
  visible: boolean;
  goTravelData: Partial<GoTravelData>;
}

const GoTravel = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();

  const goBack = () => {
    navigation.goBack();
  };

  const queryClient = useQueryClient();

  const tabs = ['Find Goraqt', 'Racquet Resorts', 'Staff Picks'];

  const [activeTab, setActiveTab] = useState('Find Goraqt');
  const [isVideoActionsModalVisible, setIsVideoActionsModalVisible] = useState<VideoModalState>({
    visible: false,
    goTravelData: {},
  });

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const {
    data: goTravelResponse,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['go-travel', activeTab],
    queryFn: async () => {
      const response = await getCommunityData('go-travel', activeTab);
      return response as ApiResponse;
    },
  });

  // Extract the data array from the API response
  const goTravelData: GoTravelData[] = goTravelResponse || [];

  const updateVideoModalState = (id: string, isLiked: boolean) => {
    // Update modal data if it's the same item
    if (isVideoActionsModalVisible.goTravelData?.id === id) {
      setIsVideoActionsModalVisible(prev => ({
        ...prev,
        goTravelData: {
          ...prev.goTravelData,
          is_liked: isLiked,
          likes_count: isLiked
            ? ((prev.goTravelData.likes_count as number) || 0) + 1
            : ((prev.goTravelData.likes_count as number) || 0) - 1,
        },
      }));
    }
  };

  const {mutate: updateLike} = useMutation(
    setupCommunityMutation<GoTravelData>(queryClient, ['go-travel'], updateVideoModalState),
  );

  const handleLikePress = (item: GoTravelData) => {
    updateLike({
      id: item.id,
      is_liked: !item.is_liked,
    });
  };

  const renderComponent = ({item}: {item: GoTravelData}) => {
    return (
      <View>
        <CommunityCard
          data={item}
          onCardPress={() => {
            navigation.navigate('CommunityDetails', {
              from: 'GoTravel',
              title: item.title,
              data: item,
            });
          }}
          onLikePress={() => {}}
          onCommentPress={() => {}}
          onSharePress={() => {}}
          containerStyle={styles.cardContainer}
          variant="tag"
          onMoreIconPress={() => {
            setIsVideoActionsModalVisible({
              visible: true,
              goTravelData: item,
            });
          }}
        />
        {item.buttonText && (
          <TouchableOpacity style={styles.blueButton}>
            <Typography variant="communityBtnText" color={theme.colors.white}>
              {item.buttonText}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <MediaCard source={Images.goTravelImage} imageContainerStyle={styles.imageContainer} />

      <View style={styles.tabContainer}>
        <Tabs
          variant="square"
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={handleTabPress}
          tabStyle={styles.tabStyle}
          tabTitleStyle={styles.tabTitleStyle}
          type="inner"
          listContainerStyle={styles.listContainerStyle}
        />
      </View>
    </View>
  );

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeBottom={false} style={styles.root}>
        <Header
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          title="GoTravels"
          backgroundColor="transparent"
          isBackPress={true}
          backNavigation={goBack}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
        />
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={goTravelData}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderComponent}
            contentContainerStyle={styles.content}
            onEndReachedThreshold={0.5}
            ListHeaderComponent={renderHeader}
            refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
            ListEmptyComponent={
              <NoData
                title={`No ${activeTab} Data`}
                message={`No ${activeTab.toLowerCase()} content available at the moment.`}
              />
            }
          />
        )}
      </SafeAreaView>
      <VideoActionsModal
        visible={isVideoActionsModalVisible.visible}
        onClose={() =>
          setIsVideoActionsModalVisible({
            visible: false,
            goTravelData: {},
          })
        }
        data={isVideoActionsModalVisible.goTravelData}
        videoTitle="Latest Reviews"
        isLiked={isVideoActionsModalVisible.goTravelData?.is_liked ?? false}
        videoThumbnail="https://picsum.photos/200/300"
        onLike={() => {
          if (isVideoActionsModalVisible.goTravelData?.id) {
            handleLikePress(isVideoActionsModalVisible.goTravelData as GoTravelData);
          }
        }}
        onComment={() => {
          navigation.navigate('CommentScreen', {
            from: 'Latest Reviews',
            title: 'Latest Reviews',
            data: goTravelData?.[0],
          });
          setIsVideoActionsModalVisible({
            visible: false,
            goTravelData: {},
          });
        }}
        onShare={() => {}}
      />
    </ImageBackground>
  );
};

export default GoTravel;
