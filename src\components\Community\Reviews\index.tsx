import React, {useState} from 'react';
import {View, ScrollView, Dimensions, TouchableOpacity, ImageBackground} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import CommunityCard from '@/components/CommunityCard';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;
import Icon from '@/components/CIcon';
import {Images} from '@/config';
import {CButton, CustomModal, RadioSelect, NoData} from '@/components';
import MediaCard from '@/components/MediaCard';
import VideoActionsModal from '@/components/VideoActionsModal';
import SearchInput from '@/components/SearchInput';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {getCommunityData, setupCommunityMutation, toaster} from '@/utils/commonFunctions';
import useTranslation from '@/hooks/useTranslation';

interface GoFitTag {
  id: number;
  name: string;
  title: string;
}

interface ReviewData {
  id: string;
  title: string;
  description: string;
  image: string;
  file_thumbnail: string;
  tags: GoFitTag[];
  is_liked: boolean;
  likes_count: number;
  [key: string]: any;
}

interface ApiResponse {
  status: boolean;
  message: string;
  data: ReviewData[];
}

interface VideoActionsModalState {
  visible: boolean;
  reviewData: ReviewData | null;
}

const Reviews: React.FC = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedSport, setSelectedSport] = useState('Tennis');
  const [selectedCategory, setSelectedCategory] = useState('Racquets');
  const [isVideoActionsModalVisible, setIsVideoActionsModalVisible] =
    useState<VideoActionsModalState>({
      visible: false,
      reviewData: null,
    });
  console.log('isVideoActionsModalVisible', isVideoActionsModalVisible);
  const clearSearch = () => {
    setSearchQuery('');
  };
  const dummyArray = [
    {
      image: 'https://picsum.photos/200/300',
      title: 'Volley exercises for beginners',
      description: 'for better performance at the net!',
    },
    {
      image: 'https://picsum.photos/200/300',
      title: 'The perfect drill',
      description: 'to build on-court speed and conditioning Go!',
    },
    {
      image: 'https://picsum.photos/200/300',
      title: 'The perfect drill',
      description: 'to build on-court speed and conditioning Go!',
    },
  ];

  const {t} = useTranslation();

  const options = [
    {label: 'Tennis', type: 'sport'},
    {label: 'Pickleball', type: 'sport'},
    {label: 'Padel', type: 'sport'},
    {label: 'Platform Tennis', type: 'sport'},
    {label: 'Racquets', type: 'category'},
    {label: 'Balls', type: 'category'},
    {label: 'Gear', type: 'category'},
    {label: 'Apparel', type: 'category'},
  ];

  const {data: reviewData, isLoading} = useQuery<ReviewData[]>({
    queryKey: ['reviews'],
    queryFn: async () => {
      const response = await getCommunityData('reviews' as string);
      return response as ReviewData[];
    },
  });

  const queryClient = useQueryClient();

  const updateVideoModalState = (id: string, isLiked: boolean) => {
    // Update modal data if it's the same item
    if (isVideoActionsModalVisible.reviewData?.id === id) {
      setIsVideoActionsModalVisible(prev => ({
        ...prev,
        reviewData: prev.reviewData
          ? {
              ...prev.reviewData,
              is_liked: isLiked,
              likes_count: isLiked
                ? (prev.reviewData.likes_count || 0) + 1
                : (prev.reviewData.likes_count || 0) - 1,
            }
          : null,
      }));
    }
  };

  const {mutate: updateLike} = useMutation({
    ...setupCommunityMutation<ReviewData>(queryClient, ['reviews'], updateVideoModalState),
    onError: (error: any) => {
      toaster('error', error.message || 'Failed to update like', 'top');
    },
  });

  const handleLikePress = (item: ReviewData) => {
    updateLike({
      id: item.id,
      is_liked: !item.is_liked,
    });
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.content}>
          <MediaCard
            source={'https://picsum.photos/200/300'}
            onLikePress={() => {}}
            onCommentPress={() => {
              navigation.navigate('CommentScreen', {
                from: 'Latest Reviews',
                title: 'Latest Reviews',
                data: dummyArray[0],
              });
            }}
            onUploadPress={() => {}}
          />
          <View style={styles.headerContainer}>
            <Typography variant="subTitle4" style={styles.headerTitle}>
              {t('reviewsScreen.title')}
            </Typography>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                setIsModalVisible(true);
              }}>
              <Icon name="filter" color={theme.colors.white} />
            </TouchableOpacity>
          </View>
          <View style={styles.cardContainer}>
            {!isLoading && reviewData && reviewData.length > 0 ? (
              reviewData.map((item: ReviewData, index: number) => (
                <CommunityCard
                  key={index.toString()}
                  variant="tag"
                  data={{
                    ...item,
                    description: item.description || '',
                    image: item.image || '',
                    file_thumbnail: item.file_thumbnail || '',
                    tags: item.tags || [],
                  }}
                  onCardPress={() => {
                    navigation.navigate('CommunityDetails', {
                      from: 'Latest Reviews',
                      title: item.title,
                      data: item,
                    });
                  }}
                  onMorePress={() => {}}
                  onLikePress={() => handleLikePress(item)}
                  onCommentPress={() => {}}
                  onSharePress={() => {}}
                  onMoreIconPress={() => {
                    setIsVideoActionsModalVisible({
                      visible: true,
                      reviewData: item,
                    });
                  }}
                  showMore={false}
                />
              ))
            ) : !isLoading ? (
              <NoData
                title={t('reviewsScreen.noReviewsFound')}
                message={t('reviewsScreen.noReviewsFoundMessage')}
              />
            ) : null}
          </View>
        </ScrollView>
      </View>
      <CustomModal
        variant="bottom"
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        title={t('reviewsScreen.refineResults')}
        titleStyle={{color: theme.colors.white}}
        modalContainerStyle={{minHeight: Dimensions.get('screen').height * 0.5}}
        showCloseButton>
        <ScrollView showsVerticalScrollIndicator={false}>
          <SearchInput
            variant="light"
            containerStyle={styles.searchContainer}
            placeholder={t('reviewsScreen.searchPlaceholder')}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onClear={clearSearch}
            placeholderTextColor={theme.colors.primary}
            inputStyle={styles.searchInput}
            iconColor={theme.colors.primary}
          />
          {(() => {
            let lastType: string | null = null;
            return options.map((option, idx) => {
              const showHeader = option.type !== lastType;
              lastType = option.type;
              return (
                <React.Fragment key={option.label}>
                  {showHeader && (
                    <Typography
                      variant="subtitle"
                      style={{
                        color: theme.colors.offWhite,
                        marginTop: idx === 0 ? 0 : 16,
                      }}>
                      Show
                    </Typography>
                  )}
                  <RadioSelect
                    label={option.label}
                    selected={
                      option.type === 'sport'
                        ? selectedSport === option.label
                        : selectedCategory === option.label
                    }
                    onPress={() =>
                      option.type === 'sport'
                        ? setSelectedSport(option.label)
                        : setSelectedCategory(option.label)
                    }
                  />
                </React.Fragment>
              );
            });
          })()}
          <CButton
            title={`80 ${t('reviewsScreen.results')}`}
            onPress={() => setIsModalVisible(false)}
            variant="primary"
            containerStyle={styles.btn}
          />
        </ScrollView>
      </CustomModal>

      <VideoActionsModal
        visible={isVideoActionsModalVisible.visible}
        onClose={() =>
          setIsVideoActionsModalVisible({
            visible: false,
            reviewData: null,
          })
        }
        data={isVideoActionsModalVisible.reviewData}
        videoTitle="Latest Reviews"
        videoThumbnail="https://picsum.photos/200/300"
        onLike={() => {
          if (isVideoActionsModalVisible.reviewData) {
            handleLikePress(isVideoActionsModalVisible.reviewData);
          }
        }}
        isLiked={isVideoActionsModalVisible.reviewData?.is_liked ?? false}
        onComment={() => {
          navigation.navigate('CommentScreen', {
            from: 'Latest Reviews',
            title: 'Latest Reviews',
            data: dummyArray[0],
          });
          setIsVideoActionsModalVisible({
            visible: false,
            reviewData: null,
          });
        }}
        onShare={() => {}}
      />
    </ImageBackground>
  );
};

export default Reviews;
