import React from 'react';
import {View, FlatList, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {Icon, SafeAreaView} from '@/components';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import createStyles from './styles';
import Typography from '@/components/Typography';
import PlayerProfileCard from '../PlayerProfileCard';
import {Images} from '@/config';

interface OptionItem {
  id: string;
  label: string;
}

const PlayerList = ({onFilterPress}: {onFilterPress: () => void}) => {
  const theme = useThemeStore();
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const styles = createStyles(theme);

  // Filter state

  const options: OptionItem[] = [
    {id: '1', label: 'Myself'},
    {id: '2', label: 'My Children'},
    {id: '3', label: 'Someone else'},
    {id: '4', label: 'Group'},
  ];

  const renderItem = ({item}: {item: OptionItem}) => {
    return (
      <PlayerProfileCard
        name={'Jed'}
        primaryCourts={'Central Park Tennis, Manhattan, NY'}
        avatar={Images.profile1}
        isPremium={true}
        onPress={() => navigation.navigate('CoachProfile')}
      />
    );
  };

  return (
    <SafeAreaView includeBottom={false} style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: 16,
        }}>
        <Typography variant="subtitle" color={theme.colors.white} style={styles.title}>
          Search Results
        </Typography>
        <TouchableOpacity activeOpacity={0.8} onPress={() => onFilterPress()}>
          <Icon name="filter" size={24} color={theme.colors.white} />
        </TouchableOpacity>
      </View>
      <FlatList
        data={options}
        renderItem={renderItem}
        keyExtractor={item => item?.id}
        contentContainerStyle={styles.pillContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </SafeAreaView>
  );
};

export default PlayerList;
