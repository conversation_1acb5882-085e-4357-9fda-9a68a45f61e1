import React, {useState} from 'react';
import {TouchableOpacity, View, ImageSourcePropType} from 'react-native';
import {styles as CreateStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Images} from '@/config';
import Typography from '../Typography';
import {CButton, Icon, CImage} from '@/components';

interface PlayerSchedulePlayCardProps {
  playerName: string;
  rating?: number;
  location: string;
  image?: ImageSourcePropType | string;
  onPress?: (e: any) => void;
}

const PlayerSchedulePlayCard: React.FC<PlayerSchedulePlayCardProps> = ({
  playerName,
  rating,
  location,
  image,
  onPress,
}) => {
  const theme = useThemeStore();
  const styles = CreateStyles(theme);

  const [isSelected, setIsSelected] = useState(false);

  const handleSelect = () => {
    setIsSelected(!isSelected);
  };

  // Render either CImage for remote images or Image for local images
  const renderImage = () => {
    if (typeof image === 'string' && image.startsWith('http')) {
      return <CImage source={{uri: image}} style={styles.image} fallbackSource={Images.profile1} />;
    }
    return <CImage source={image} style={styles.image} resizeMode="contain" />;
  };

  return (
    <View style={styles.container}>
      <View style={styles.playerContainer}>
        <View style={styles.imageContainer}>{renderImage()}</View>
        <View style={styles.playerInfoContainer}>
          <View style={{flex: 1}}>
            <Typography variant="coachTitle" color={theme.colors.white}>
              {playerName}{' '}
              <Typography variant="badgeText" color={theme.colors.white} style={styles.rating}>
                {rating}
              </Typography>
            </Typography>
            <View style={styles.locationInfo}>
              <Icon name="location-pin" size={20} color={theme.colors.activeColor} />
              <Typography variant="gameCardDescription" color={theme.colors.white}>
                {location}
              </Typography>
            </View>
          </View>
          <TouchableOpacity
            style={[
              styles.plusButton,
              {
                backgroundColor: isSelected ? theme.colors.activeColor : theme.colors.primary,
              },
            ]}
            onPress={handleSelect}>
            <Icon
              name={isSelected ? 'check' : 'create-group'}
              size={isSelected ? 26 : 20}
              color={isSelected ? theme.colors.background : theme?.colors?.text}
            />
          </TouchableOpacity>
        </View>
      </View>
      <CButton
        title="Schedule Play"
        variant="primary"
        onPress={onPress}
        containerStyle={styles.schedulePlayButton}
        textStyle={styles.schedulePlayButtonText}
      />
    </View>
  );
};

export default PlayerSchedulePlayCard;
