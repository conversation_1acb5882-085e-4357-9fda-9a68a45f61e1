import React, {useState, useEffect, useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {StyleSheet, BackHandler} from 'react-native';
import {DEFAULT_REGION, getCurrentRegion} from '@/utils/locationService';
import BottomSheet from '@gorhom/bottom-sheet';
import FindPlayers from '@/components/FindPlayer';
import ParksSearchScreen from '@/components/ParksSearch';
import {NavigationProp, useNavigation} from '@react-navigation/native';

// Define the Player type to match what's in FindPlayers component
type Player = {
  id: string;
  name: string;
  rating: string;
  location: string;
  image: string;
  color?: string;
  isPremium?: boolean;
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    bottomSheet: {
      paddingHorizontal: 16,
      paddingTop: 10,
    },
    parksSearchSheet: {
      paddingTop: 20,
      paddingHorizontal: 16,
    },
  });

const PlayerConnectScreen = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [initialRegion, setInitialRegion] = useState(DEFAULT_REGION);
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>([]);
  const [currentMode, setCurrentMode] = useState<'findPlayers' | 'parksSearch'>('findPlayers');

  const bottomSheetRef = useRef<BottomSheet>(null);

  const navigation = useNavigation<NavigationProp<any>>();

  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const region = await getCurrentRegion();
        console.log('Initial region:', region);
        setInitialRegion(region);
      } catch (error) {
        console.error('Error getting user location:', error);
      }
    };

    fetchUserLocation();
  }, []);

  // Handle hardware back button press
  useEffect(() => {
    const backAction = () => {
      if (currentMode === 'parksSearch') {
        setCurrentMode('findPlayers');
        setTimeout(() => {
          if (bottomSheetRef.current) {
            bottomSheetRef.current.expand();
          }
        }, 300);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove(); // Clean up the event listener
  }, [currentMode]);

  const handleConfirm = (players: Player[]) => {
    setSelectedPlayers(players);
    setCurrentMode('parksSearch');
  };

  const handleParkSelect = (parkData: object) => {
    navigation.navigate('PlayerConnectDateScreen', {
      parkData: parkData,
      selectedPlayersData: selectedPlayers,
    });
  };

  // Get snap points based on the current mode
  const getSnapPoints = () => {
    return currentMode === 'findPlayers' ? [220, 600] : [170, 500];
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      snapPoints={getSnapPoints()}
      enableOverDrag={false}
      keyboardBehavior="interactive"
      keyboardBlurBehavior="restore"
      enablePanDownToClose={false}
      animateOnMount
      style={currentMode === 'findPlayers' ? styles.bottomSheet : styles.parksSearchSheet}
      backgroundStyle={{backgroundColor: theme.colors.background}}
      handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
      onChange={index => {
        if (index === -1 && currentMode === 'parksSearch') {
          bottomSheetRef.current?.snapToIndex(0);
        }
      }}>
      {currentMode === 'findPlayers' ? (
        <FindPlayers onConfirm={handleConfirm} initialSelectedPlayers={selectedPlayers} />
      ) : (
        <ParksSearchScreen
          onParkSelect={handleParkSelect}
          type="playerConnect"
          onBackPress={() => {
            setCurrentMode('findPlayers');
          }}
        />
      )}
    </BottomSheet>
  );
};

export default PlayerConnectScreen;
