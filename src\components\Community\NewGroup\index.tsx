import React from 'react';
import {View, ScrollView, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {createStyles} from './Styles';
import CIcon from '@/components/CIcon';
import CInput from '@/components/CInput';
import * as yup from 'yup';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

const schema = yup.object().shape({
  groupName: yup.string().required('Group name is required'),
  privacy: yup.string().oneOf(['Public', 'Private', 'Hidden']).required(),
  favoriteLocation: yup.string().required('Favorite location is required'),
  tags: yup.string().required('Tags are required'),
});

type FormValues = {
  groupName: string;
  privacy: 'Public' | 'Private' | 'Hidden';
  favoriteLocation: string;
  tags: string;
};

const NewGroup: React.FC = ({route}: {route: any}) => {
  const {groupName} = route.params || {};
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();

  const {t} = useTranslation();

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      groupName: groupName || '',
      privacy: 'Public',
      favoriteLocation: '',
      tags: '',
    },
  });

  const onSubmit = (data: FormValues) => {
    // handle form data
    console.log('Form Data:', data);
  };

  return (
    <SafeAreaView
      includeTop={true}
      includeBottom={false}
      style={{flex: 1, backgroundColor: theme.colors.background}}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Typography variant="parkTitle" color={theme.colors.primary}>
              {t('common.back')}
            </Typography>
          </TouchableOpacity>
          <Typography variant="parkTitle" color={theme.colors.white}>
            {t('newGroupScreen.newGroup')}
          </Typography>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('AddMembers', {groupName: control._formValues.groupName})
            }>
            {/* <TouchableOpacity onPress={handleSubmit(onSubmit)}> */}
            <Typography variant="parkTitle" color={theme.colors.activeColor}>
              {t('common.create')}
            </Typography>
          </TouchableOpacity>
        </View>

        {/* Group Name with Camera Icon */}
        <View
          style={[styles.groupNameContainer, {marginBottom: errors.groupName?.message ? 0 : 25}]}>
          <TouchableOpacity style={styles.cameraCircle}>
            <CIcon name="camera-1" size={32} color={theme.colors.black} />
          </TouchableOpacity>
          <Controller
            control={control}
            name="groupName"
            render={({field: {onChange, value, onBlur}}) => (
              <View style={{flex: 1}}>
                <CInput
                  containerStyle={styles.groupNameInput}
                  placeholder={t('newGroupScreen.groupNamePlaceholder')}
                  inputStyle={styles.groupNameInput}
                  placeholderTextColorStyle={theme.colors.activeColor}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                />
              </View>
            )}
          />
        </View>
        {errors.groupName?.message && (
          <Typography style={[styles.errorText, {marginBottom: 20}]}>
            {errors.groupName?.message}
          </Typography>
        )}

        <Typography variant="notificationText" color={theme.colors.white}>
          {t('newGroupScreen.groupSettings')}
        </Typography>
        <Typography style={{marginTop: 25}} variant="notificationText" color={theme.colors.white}>
          {t('newGroupScreen.privacy')}
        </Typography>
        <Controller
          control={control}
          name="privacy"
          render={({field: {onChange, value}}) => (
            <View style={styles.privacyRow}>
              {[t('common.public'), t('common.private'), t('common.hidden')].map(option => (
                <TouchableOpacity
                  activeOpacity={0.7}
                  key={option}
                  style={[styles.privacyButton, value === option && styles.privacyButtonSelected]}
                  onPress={() => onChange(option as 'Public' | 'Private' | 'Hidden')}>
                  <Typography
                    variant="tryNow"
                    style={[
                      styles.privacyButtonText,
                      value === option ? styles.privacyButtonTextSelected : undefined,
                    ]}>
                    {option}
                  </Typography>
                </TouchableOpacity>
              ))}
            </View>
          )}
        />
        {errors.privacy && (
          <Typography style={{color: theme.colors.red, marginBottom: 8}}>
            {errors.privacy.message}
          </Typography>
        )}

        <Controller
          control={control}
          name="favoriteLocation"
          render={({field: {onChange, value, onBlur}}) => (
            <CInput
              showLabel={true}
              label={t('newGroupScreen.favoriteLocation')}
              labelStyle={styles.label}
              inputStyle={styles.input}
              placeholder={t('newGroupScreen.favoriteLocationPlaceholder')}
              placeholderTextColor={theme.colors.activeColor}
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.favoriteLocation?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="tags"
          render={({field: {onChange, value, onBlur}}) => (
            <CInput
              showLabel={true}
              label={t('newGroupScreen.tags')}
              labelStyle={styles.label}
              inputStyle={styles.input}
              placeholder={t('newGroupScreen.tags')}
              placeholderTextColor={theme.colors.activeColor}
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.tags?.message}
            />
          )}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default NewGroup;
