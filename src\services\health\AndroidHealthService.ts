import {Platform} from 'react-native';
import GoogleFit, {Scopes, BucketUnit} from 'react-native-google-fit';
import {HealthServiceInterface} from './types';
import {format} from 'date-fns';

/**
 * Android implementation of the health service using Google Fit
 */
class AndroidHealthService implements HealthServiceInterface {
  private isInitialized = false;
  private stepGoal = 10000; // Default step goal

  /**
   * Initialize Google Fit
   */
  async initialize(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    if (this.isInitialized) {
      return true;
    }

    // Check if GoogleFit is available
    if (!GoogleFit) {
      console.error('GoogleFit is not available');
      return false;
    }

    try {
      // Set up authorization options with minimal required scopes
      // Using too many scopes can cause authorization to fail
      const options = {
        scopes: [Scopes.FITNESS_ACTIVITY_READ],
      };

      console.log('Starting Google Fit authorization with minimal scopes');

      // Make sure authorize method exists
      if (typeof GoogleFit.authorize !== 'function') {
        console.error('GoogleFit.authorize is not a function');
        return false;
      }

      // Use a simple, direct approach for authorization
      return new Promise<boolean>(resolve => {
        try {
          // Set a timeout to prevent hanging
          const timeoutId = setTimeout(() => {
            console.log('Authorization timed out, assuming failure');
            resolve(false);
          }, 30000);

          // Use the direct approach
          GoogleFit.authorize(options)
            .then((authResult: {success: boolean; message?: string}) => {
              clearTimeout(timeoutId);

              console.log('Google Fit authorization result:', authResult);

              if (authResult && authResult.success) {
                console.log('Google Fit authorization successful');
                this.isInitialized = true;
                resolve(true);
              } else {
                console.error(
                  'Google Fit authorization failed:',
                  authResult ? authResult.message : 'Unknown error',
                );
                resolve(false);
              }
            })
            .catch((error: Error) => {
              clearTimeout(timeoutId);
              console.error('Error during Google Fit authorization:', error);
              resolve(false);
            });
        } catch (error) {
          console.error('Exception during Google Fit authorization:', error);
          resolve(false);
        }
      });
    } catch (error) {
      console.error('Error initializing Google Fit:', error);
      return false;
    }
  }

  /**
   * Request permissions for Google Fit
   */
  async requestPermissions(): Promise<boolean> {
    return this.initialize();
  }

  /**
   * Check if Google Fit permissions are granted
   */
  async hasPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    // If already initialized, we have permissions
    if (this.isInitialized) {
      return true;
    }

    // Check if GoogleFit is available
    if (!GoogleFit) {
      console.error('GoogleFit is not available');
      return false;
    }

    try {
      // First check the isAuthorized property
      if (GoogleFit.isAuthorized) {
        this.isInitialized = true;
        return true;
      }

      // Try to check authorization status
      if (typeof GoogleFit.checkIsAuthorized === 'function') {
        try {
          await GoogleFit.checkIsAuthorized();
          if (GoogleFit.isAuthorized) {
            this.isInitialized = true;
            return true;
          }
        } catch (checkError) {
          console.log('Error in checkIsAuthorized:', checkError);
          // Continue to next method
        }
      }

      // As a last resort, try to initialize
      // This might trigger the authorization dialog
      console.log('Checking permissions by initializing Google Fit');
      return this.initialize();
    } catch (error) {
      console.error('Error checking Google Fit permissions:', error);
      return false;
    }
  }

  /**
   * Get step count for a specific date
   */
  async getStepCount(date: Date = new Date()): Promise<{count: number; date: string}> {
    if (Platform.OS !== 'android') {
      return {
        count: 0,
        date: format(date, 'yyyy-MM-dd'),
      };
    }

    // Try to initialize if not already initialized
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        console.error('Failed to initialize Google Fit');
        return {
          count: 0,
          date: format(date, 'yyyy-MM-dd'),
        };
      }
    }

    // Check if GoogleFit is available
    if (!GoogleFit) {
      console.error('GoogleFit is not available');
      return {
        count: 0,
        date: format(date, 'yyyy-MM-dd'),
      };
    }

    try {
      // Create a new date object to avoid mutating the original date
      const dateObj = new Date(date);
      const formattedDate = format(dateObj, 'yyyy-MM-dd');

      // Create start and end date for the day
      const startDate = new Date(dateObj);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(dateObj);
      endDate.setHours(23, 59, 59, 999);

      const options = {
        startDate: startDate.toISOString(), // Start of the day
        endDate: endDate.toISOString(), // End of the day
        bucketUnit: BucketUnit.DAY,
        bucketInterval: 1,
      };

      // Make sure getDailyStepCountSamples method exists
      if (typeof GoogleFit.getDailyStepCountSamples !== 'function') {
        console.error('GoogleFit.getDailyStepCountSamples is not a function');
        return {
          count: 0,
          date: formattedDate,
        };
      }

      const res = await GoogleFit.getDailyStepCountSamples(options);

      // Find the steps from the most reliable source
      let steps = 0;

      if (!res || !Array.isArray(res)) {
        console.error('Invalid response from GoogleFit.getDailyStepCountSamples');
        return {
          count: 0,
          date: formattedDate,
        };
      }

      // First try to get steps from Google Fit's own source
      const googleFitSource = res.find(
        source => source.source === 'com.google.android.gms:estimated_steps',
      );
      if (googleFitSource && googleFitSource.steps && googleFitSource.steps.length > 0) {
        steps = googleFitSource.steps[0].value;
      } else {
        // Try other sources if Google Fit source is not available
        for (const source of res) {
          if (source.steps && source.steps.length > 0) {
            steps = source.steps[0].value;
            break;
          }
        }
      }

      return {
        count: steps,
        date: formattedDate,
      };
    } catch (error) {
      console.error('Error getting step count:', error);
      return {
        count: 0,
        date: format(date, 'yyyy-MM-dd'),
      };
    }
  }

  /**
   * Get heart rate for a specific date
   */
  async getHeartRate(
    date: Date = new Date(),
  ): Promise<{value: number; unit: string; date: string}> {
    if (Platform.OS !== 'android') {
      return {
        value: 0,
        unit: 'bpm',
        date: format(date, 'yyyy-MM-dd'),
      };
    }

    // Try to initialize if not already initialized
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        console.error('Failed to initialize Google Fit');
        return {
          value: 0,
          unit: 'bpm',
          date: format(date, 'yyyy-MM-dd'),
        };
      }
    }

    // Check if GoogleFit is available
    if (!GoogleFit) {
      console.error('GoogleFit is not available');
      return {
        value: 0,
        unit: 'bpm',
        date: format(date, 'yyyy-MM-dd'),
      };
    }

    try {
      // Create a new date object to avoid mutating the original date
      const dateObj = new Date(date);
      const formattedDate = format(dateObj, 'yyyy-MM-dd');

      // Create start and end date for the day
      const startDate = new Date(dateObj);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(dateObj);
      endDate.setHours(23, 59, 59, 999);

      const options = {
        startDate: startDate.toISOString(), // Start of the day
        endDate: endDate.toISOString(), // End of the day
        bucketUnit: BucketUnit.DAY,
        bucketInterval: 1,
      };

      // Make sure getHeartRateSamples method exists
      if (typeof GoogleFit.getHeartRateSamples !== 'function') {
        console.error('GoogleFit.getHeartRateSamples is not a function');
        return {
          value: 0,
          unit: 'bpm',
          date: formattedDate,
        };
      }

      const heartRateData = await GoogleFit.getHeartRateSamples(options);

      // Get the most recent heart rate reading
      let heartRate = 0;
      if (heartRateData && Array.isArray(heartRateData) && heartRateData.length > 0) {
        // Sort by date descending to get the most recent reading
        const sortedData = heartRateData.sort(
          (a, b) => new Date(b.endDate).getTime() - new Date(a.endDate).getTime(),
        );

        heartRate = sortedData[0].value;
      }

      return {
        value: heartRate,
        unit: 'bpm',
        date: formattedDate,
      };
    } catch (error) {
      console.error('Error getting heart rate:', error);
      return {
        value: 0,
        unit: 'bpm',
        date: format(date, 'yyyy-MM-dd'),
      };
    }
  }

  /**
   * Get step count goal
   */
  async getStepCountGoal(): Promise<number> {
    // Google Fit doesn't provide a direct API to get the step goal
    // We'll use the stored value or default
    return this.stepGoal;
  }

  /**
   * Set step count goal
   */
  async setStepCountGoal(goal: number): Promise<boolean> {
    this.stepGoal = goal;
    return true;
  }
}

export default AndroidHealthService;
