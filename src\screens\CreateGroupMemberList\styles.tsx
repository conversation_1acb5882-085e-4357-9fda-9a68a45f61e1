import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    content: {
      flex: 1,
      padding: 16,
      backgroundColor: theme.colors.background,
    },
    headerRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 10,
    },
    groupNameInput: {
      color: theme.colors.white,
      borderWidth: 0,
      marginBottom: 0,
    },
    groupNameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 10,
      padding: 16,
      marginTop: 25,
      marginBottom: 20,
      gap: 14,
    },
    cameraCircle: {
      width: 51,
      height: 51,
      borderRadius: 40,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.white,
    },
    groupSettings: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 0,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.divider,
      padding: 18,
      borderRadius: 10,
    },
    closeButton: {
      position: 'absolute',
      top: 0,
      right: -7,
      backgroundColor: theme.colors.inputLabel,
      borderRadius: 12,
      width: 20,
      height: 20,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: theme.colors.background,
    },
    memberAvatar: {
      width: 64,
      height: 64,
      borderRadius: 32,
    },
    memberContainer: {
      alignItems: 'center',
    },
    memberAvatarContainer: {
      position: 'relative',
    },
    userName: {
      marginTop: 6,
      fontSize: 14,
    },
    memberList: {
      gap: 24,
      paddingVertical: 10,
    },
    selectedMembersContainer: {
      marginTop: 16,
    },
    selectedMembersTitle: {
      marginBottom: 10,
    },
  });
