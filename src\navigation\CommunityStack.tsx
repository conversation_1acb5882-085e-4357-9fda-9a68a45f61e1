import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {CommunityScreen} from '@/screens/tabs';
export type CommunityStackParamList = {
  CommunityHome:
    | {
        type?: string;
        data?: any;
      }
    | undefined;
  PlayerConnectScreen: undefined;
  GoFit: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  MyGroups: undefined;
  NewGroup: {
    groupName?: string;
  };
  JoinGroups: undefined;
  JoinGroupDetails: undefined;
  AddMembers: {
    groupName: string;
  };
  CreateGroupMemberList: {
    groupName: string;
    data: string[];
  };
  Chat: undefined;
  PlayerConnectDateScreen: undefined;
};

const Stack = createNativeStackNavigator<CommunityStackParamList>();

// Community stack with bottom sheet
const CommunityScreenWithBottomSheet = ({route}: {route: any}) => <CommunityScreen route={route} />;

const CommunityStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="CommunityHome"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true, // Enable gesture navigation
        gestureDirection: 'horizontal', // iOS style (default)
        gestureResponseDistance: {start: 50, end: -1}, // Distance from edge to trigger gesture
      }}>
      <Stack.Screen
        name="CommunityHome"
        component={CommunityScreenWithBottomSheet}
        options={{animation: 'fade'}}
      />
    </Stack.Navigator>
  );
};

export default CommunityStack;
