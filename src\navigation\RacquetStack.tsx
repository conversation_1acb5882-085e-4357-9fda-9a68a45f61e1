import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RacquetsScreen} from '@/screens/tabs';
import RacquetCategoryScreen from '@/screens/tabs/RacquetsScreens/RacqetsCategory';
import RacquetsBrandsScreen from '@/screens/tabs/RacquetsScreens/Brands';
import RacquetSelector from '@/screens/tabs/RacquetsScreens/RacquetSelector';
import RacquetSelectorDetail from '@/screens/tabs/RacquetsScreens/RacquetSelectorDetail';
import TestSwipeBackScreen from '@/screens/TestSwipeBack';

export type RacquetStackParamList = {
  RacquetsScreen: undefined;
  RacquetCategory: {
    sportsTitle: string;
  };
  RacquetBrands: {
    sportsTitle: string;
    category: string;
  };
  RacquetSelector: undefined;
  RacquetSelectorDetail: undefined;
  TestSwipeBack: undefined;
};

const Stack = createNativeStackNavigator<RacquetStackParamList>();

const RacquetStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="RacquetsScreen"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true, // Enable gesture navigation
        gestureDirection: 'horizontal', // iOS style (default)
        gestureResponseDistance: {start: 50, end: -1}, // Distance from edge to trigger gesture
      }}>
      <Stack.Screen
        name="RacquetsScreen"
        component={RacquetsScreen}
        options={{animation: 'slide_from_right'}}
      />
      <Stack.Screen
        name="RacquetCategory"
        component={RacquetCategoryScreen}
        options={{animation: 'slide_from_right'}}
      />
      <Stack.Screen
        name="RacquetBrands"
        component={RacquetsBrandsScreen}
        options={{animation: 'slide_from_right'}}
      />
      <Stack.Screen
        name="RacquetSelector"
        component={RacquetSelector}
        options={{animation: 'slide_from_right'}}
      />
      <Stack.Screen
        name="RacquetSelectorDetail"
        component={RacquetSelectorDetail}
        options={{animation: 'slide_from_right'}}
      />
      <Stack.Screen
        name="TestSwipeBack"
        component={TestSwipeBackScreen}
        options={{animation: 'slide_from_right'}}
      />
    </Stack.Navigator>
  );
};

export default RacquetStack;
