import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';

interface SwipeBackGestureProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
}

const SwipeBackGesture: React.FC<SwipeBackGestureProps> = ({
  children,
  enabled = true,
  threshold = 100,
}) => {
  const navigation = useNavigation();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);

  const goBack = () => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .failOffsetX([-Number.MAX_SAFE_INTEGER, -15])
    .failOffsetY([-20, 20])
    .onUpdate(event => {
      // Only allow right swipe (positive translation)
      if (event.translationX > 0) {
        translateX.value = event.translationX;
        // Reduce opacity as user swipes
        opacity.value = Math.max(0.3, 1 - event.translationX / 300);
      }
    })
    .onEnd(event => {
      const shouldGoBack = event.translationX > threshold && event.velocityX > 0;

      if (shouldGoBack) {
        // Animate out and go back
        translateX.value = withSpring(300, {damping: 20}, () => {
          runOnJS(goBack)();
        });
        opacity.value = withSpring(0);
      } else {
        // Animate back to original position
        translateX.value = withSpring(0);
        opacity.value = withSpring(1);
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{translateX: translateX.value}],
      opacity: opacity.value,
    };
  });

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.content, animatedStyle]}>{children}</Animated.View>
      </GestureDetector>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default SwipeBackGesture;
