{"//": "German (Deutsch) translations for the application", "common": {"ok": "OK", "cancel": "Abbrechen", "back": "Zurück", "next": "<PERSON><PERSON>", "save": "Speichern", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "loading": "Lädt...", "error": "<PERSON><PERSON>", "success": "Erfolgreich", "search": "<PERSON><PERSON>", "SEARCH": "SUCHEN", "community": "GEMEINSCHAFT", "kiosk": "KIOSK", "filter": "Filtern", "apply": "<PERSON><PERSON><PERSON>", "reset": "Z<PERSON>ücksetzen", "close": "Schließen", "upload": "Hochladen", "invite": "Einladen", "uploadContacts": "Kontakte hochladen", "enterName": "<PERSON>n e<PERSON>ben", "enterEmail": "E-Mail-Ad<PERSON><PERSON>", "enterPhone": "Telefonnummer eingeben", "enterAtpRating": "ATP-Ranking e<PERSON>ben (Optional)", "on": "EIN", "off": "AUS", "create": "<PERSON><PERSON><PERSON><PERSON>", "public": "<PERSON><PERSON><PERSON><PERSON>", "private": "Privat", "hidden": "Versteckt", "skip": "Überspringen", "age": "Alter", "reportAbuse": "<PERSON><PERSON><PERSON> melden", "inviteMore": "Mehr e<PERSON>laden!", "inviteSent": "Einladung wurde gesendet!"}, "auth": {"login": "Anmelden", "signup": "Registrieren", "email": "E-Mail", "password": "Passwort", "forgotPassword": "Passwort vergessen?", "loginWithEmail": "Mit E-Mail anmelden", "loginWithGoogle": "Mit Google anmelden", "loginWithFacebook": "Mit Facebook anmelden", "dontHaveAccount": "Noch kein Konto?", "alreadyHaveAccount": "Bereits ein Konto?", "createAccount": "<PERSON><PERSON> er<PERSON>", "logout": "Abmelden", "verification": "Verifizierung"}, "profile": {"profile": "Profil", "settings": "Einstellungen", "personalInfo": "Persönliche Informationen", "name": "Name", "phoneNumber": "Telefonnummer", "bio": "Biografie", "updateProfile": "Profil aktualisieren", "createProfile": "<PERSON><PERSON>", "whichBestDescribesYou": "Was beschreibt Si<PERSON> am besten?"}, "settings": {"settings": "Einstellungen", "language": "<PERSON><PERSON><PERSON>", "notifications": "Benachrichtigungen", "darkMode": "Dunkler Modus", "biometrics": "Biometrie", "termsOfService": "Nutzungsbedingungen", "privacyPolicy": "Datenschutzrichtlinie", "shareLocationWithFriends": "Standort mit Freunden teilen", "twoFactorAuthentication": "Zwei-Faktor-Authentifizierung", "about": "<PERSON><PERSON>", "version": "Version", "searchSettings": "Sucheinstellungen"}, "booking": {"searchParks": "Parks suchen", "selectDateTime": "Datum und Uhrzeit wählen", "purchaseEquipments": "Ausrüstung kaufen", "cartView": "<PERSON><PERSON><PERSON>", "createProfile": "<PERSON><PERSON>", "signup": "Registrieren", "advertisement": "Werbung", "subscription": "Abonnement"}, "parks": {"findParks": "<PERSON> finden", "nearbyParks": "Parks in der Nähe", "location": "<PERSON><PERSON>", "distance": "Entfernung", "facilities": "Einrichtungen", "courts": "Pl<PERSON><PERSON>", "availableNow": "Jetzt verfügbar"}, "equipment": {"equipment": "Ausrüstung", "racquets": "SCHLÄGER", "balls": "BÄLLE", "accessories": "Zubehör", "brand": "<PERSON><PERSON>", "price": "Pre<PERSON>", "addToCart": "In den Warenkorb", "removeFromCart": "Aus Warenkorb entfernen"}, "permissions": {"locationPermissions": "Standortberechtigungen", "locationDescription": "<PERSON>se App benö<PERSON>gt <PERSON>", "enableLocation": "Standort aktivieren", "enableBluetooth": "Bluetooth aktivieren", "enableNotifications": "Benachrichtigungen aktivieren", "noThanks": "<PERSON><PERSON> danke", "bluetoothPermissions": "Bluetooth-Berechtigungen", "bluetoothDescription": "Funktioniert am besten mit aktiviertem Bluetooth", "notificationPermissions": "Benachrichtigungsberechtigungen", "notificationDescription": "Funktioniert am besten mit aktivierten Benachrichtigungen"}, "terms": {"title": "Geschäftsbedingungen", "accept": "Akzeptieren"}, "signupScreen": {"signUpWith": "Registrieren mit", "facebook": "Facebook", "google": "Google", "email": "E-Mail", "alreadyHaveAnAccount": "Bereits ein Konto?", "login": "Anmelden", "bySigningUpYouAgreeToOur": "Mit der Registrierung stimmen Sie unseren", "termsAndConditions": "Geschäftsbedingungen zu", "signin": "Anmelden"}, "login": {"title": "Anmelden", "enterYourEmail": "E-Mail eingeben", "emailAddress": "E-Mail-Adresse", "enterYourPassword": "Passwort eingeben", "password": "Passwort", "forgotYourPassword": "Passwort vergessen?", "signUp": "Registrieren", "submit": "<PERSON><PERSON>", "dontHaveAccount": "Noch kein Konto?"}, "createProfile": {"createProfileTitle": "<PERSON><PERSON><PERSON><PERSON> Sie Ihr Goraqt-Profil", "name": "Anzeigename", "namePlaceholder": "Anzeigename e<PERSON>ben", "nameError": "Name ist erforderlich", "email": "E-Mail", "emailPlaceholder": "E-Mail-Ad<PERSON><PERSON>", "emailError": "E-Mail ist erforderlich", "emailFormatError": "E-Mail-Format ist ungültig", "randomNameGenerator": "Generierte Anzeigenamen", "age": "Alter", "agePlaceholder": "<PERSON>er e<PERSON>ben", "ageError": "Alter ist erforderlich", "ageNumberError": "Alter muss eine Zahl sein", "ageLengthError": "Alter muss zwischen 13 und 120 liegen", "ageFormatError": "Altersformat ist ungültig", "userTypeError": "Bitte wählen Sie eine Option", "fitnessLevelError": "Bitte wählen Sie ein Fitnesslevel", "describe": "Beschreiben Si<PERSON> sich", "describeFitness": "Was beschreibt Ihr Fitness-/Fähigkeitslevel am besten?", "submitBtn": "Profil vervollständigen", "setupLaterBtn": "Später einrichten", "birthYear": "Geburtsjahr", "birthYearPlaceholder": "Geburtsjahr e<PERSON>ben", "birthYearformatErr": "Muss ein gültiges Jahr sein (JJJJ)", "birthYearRangeErr": "Geburtsjahr darf nicht größer als das aktuelle Jahr und nicht mehr als 60 Jahre in der Vergangenheit sein", "birthYearRequired": "Geburtsjahr ist erforderlich"}, "advertisement": {"title": "Probieren Sie 30 Tage Wilson-Schläger für 25€", "subTitle": "Sie erhalten 1 Stunde Spielzeit pro Tag für 30 Tage, 50% Rabatt auf eine Dose Wilson-Bälle und 25€ Guthaben für den Schlägerkauf", "skipBtn": "Überspringen", "claimOfferBtn": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>"}, "subscription": {"trial": "30-<PERSON><PERSON>-<PERSON><PERSON>", "price": "20,44€", "promoMessage": "Nach Ihrer kostenlosen Testphase erhalten Sie 10% Rabatt auf die Mitgliedschaft mit einer qualifizierten American Express Kredit- oder Debitkarte.", "bulletTitle": "Verkaufsargument", "bulletSubtitle": "adipiscing elit, sed do eiusmod tempor", "promoCodePlaceholder": "PROMO-CODE EINGEBEN", "termsText": "Durch den Kauf Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud", "termsLink": "Vorbehaltlich der Geschäftsbedingungen", "acceptBtn": "Akzeptieren"}, "createProfileLogin": {"title": "Hall<PERSON>", "subTitle": "Wir benötigen nur noch wenige Details zur Einrichtung.", "age": "Wie alt sind Si<PERSON>?", "agePlaceholder": "<PERSON><PERSON> Alter e<PERSON>ben", "ageRequired": "Alter ist erforderlich", "ageNumber": "Alter muss eine Zahl sein", "ageRange": "Alter muss zwischen 13 und 120 liegen", "userTypeRequired": "Bitte wählen Sie eine Option", "fitnessLevelRequired": "Bitte wählen Sie ein Fitnesslevel", "userType": "Was beschreibt Si<PERSON> am besten?", "fitnessLevel": "Was beschreibt Ihr Fitness-/Fähigkeitslevel am besten?", "completeProfile": "Profil vervollständigen", "setupLater": "Später einrichten", "birthYear": "Geburtsjahr", "birthYearPlaceholder": "Geburtsjahr e<PERSON>ben", "birthYearformatErr": "Muss ein gültiges Jahr sein (JJJJ)"}, "ParksSearchScreen": {"niceToSeeYou": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> zu <PERSON>hen", "guest": "Gas<PERSON>", "whereDoYouWantToPlay": "Wo möchten Sie spielen?", "noParksFound": "<PERSON>ine <PERSON> gefunden", "loadingMoreParks": "Weitere Parks werden geladen...", "noKiosk": "In diesem Bereich gibt es keinen Smart-Kiosk.", "recommendLocation": "Standort empfehlen", "enter": "<PERSON><PERSON> e<PERSON>ben", "address": "<PERSON><PERSON><PERSON>", "city": "Stadt", "state": "Bundesland", "submit": "<PERSON><PERSON><PERSON><PERSON>", "zipCode": "<PERSON><PERSON><PERSON><PERSON>", "nameRequired": "Name ist erforderlich", "nameInvalid": "Name darf nur Buchstaben enthalten", "addressRequired": "<PERSON>resse ist erforderlich", "cityRequired": "Stadt ist erforderlich", "cityInvalid": "Stadt darf nur Buchstaben enthalten", "stateRequired": "Bundesland ist erforderlich", "stateInvalid": "Bundesland darf nur Buchstaben enthalten", "zipCodeRequired": "Postleitzahl ist erforderlich", "invalidZipCode": "Ungültige Postleitzahl"}, "dateScreen": {"selectDateAndTime": "Datum und Uhrzeit auswählen"}, "datePicker": {"selectDateAndTime": "Datum und Uhrzeit auswählen"}, "customDatePicker": {"addPlayers": "<PERSON><PERSON><PERSON> hinzufügen", "otherPlayers": "<PERSON><PERSON>"}, "invitePlayers": {"invitePlayers": "<PERSON><PERSON><PERSON> einladen", "accept": "Akzeptieren", "friends": "Freunde", "groups": "Gruppen", "nearby": "In der Nähe", "invite,": "Einladen", "searchNameLocationGroup": "Name, Standort, Gruppe usw. suchen", "filterResults": "Ergebnisse filtern", "reserveGear": "Ausrüstung reservieren", "inviteSent": "Einladung wurde gesendet!", "close": "Schließen", "invitePlayerTabs": {"friends": "Freunde", "groups": "Gruppen", "nearby": "In der Nähe", "invite": "Einladen"}}, "ShoppingCartSection": {"yourCart": "<PERSON><PERSON>", "total": "Gesamt", "proceedToCheckout": "<PERSON><PERSON> Ka<PERSON> gehen", "emptyCart": "Ihr Warenkorb ist leer", "findAPark": "<PERSON> finden"}, "EquipmentReservation": {"filterResults": "Ergebnisse filtern", "availableNowInKiosk": "Jetzt im Kiosk verfügbar", "age": "Alter", "apply": "<PERSON><PERSON><PERSON>", "previous": "Zurück", "next": "<PERSON><PERSON>", "reserveOrPurchaseEquipment": "Ausrüstung reservieren oder kaufen"}, "EquipmentDetailModal": {"tryItNow": "JETZT AUSPROBIEREN!", "specifications": "Spezifikationen", "averageRating": "Durchschnittsbewertung", "playerReviews": "Spielerbewertungen", "more": "<PERSON><PERSON>", "less": "<PERSON><PERSON>", "staffReviews": "Mitarbeiterbewertungen"}, "RacquetsScreen": {"topBrands": "Top-<PERSON>en", "allBrands": "<PERSON>e Marken", "sports": "Sportarten", "services": "Dienstleistungen"}, "RacquetCategoryScreen": {"byBrand": "<PERSON><PERSON>", "bestForMyGame": "Am besten für mein Spiel"}, "RacquetsBrandsScreen": {"brands": "<PERSON><PERSON>"}, "RacquetSelector": {"racquetSelector": "Schläger-Auswahl", "howLongHaveYouBeenPlayingTennis": "Wie lange spielen Si<PERSON>\nschon Tennis?", "lessThan2Years": "0-2 Jahre", "moreThan2Years": "2+ <PERSON><PERSON><PERSON>", "whichBestDescribesYourFitnessLevel": "Was beschreibt Ihr\nFitnesslevel am besten?", "slowAndSteadyOnTheTreadmill": "Langsam und stetig\nauf dem Laufband", "workoutWarrior": "Fitness-\n<PERSON><PERSON><PERSON>", "whichCharacteristicsDoYouMostWantInARacquet": "Welche Eigenschaften wünschen Sie sich\nam meisten bei einem Schläger?", "comfortableAndArmFriendly": "Komfortabel\nund armschonend", "lotsOfPower": "Viel\nKraft"}, "RacquetSelectorDetail": {"RacquetSelectorDetail": "Schläger-Auswahl", "tryItNow": "JETZT AUSPROBIEREN!", "results": "Ergebnisse"}, "CartScreen": {"Subscriber": "Abonnent", "cart": "<PERSON><PERSON><PERSON>", "description": "Nach Ihrer kostenlosen Testphase erhalten Sie 10% Rabatt auf die Mitgliedschaft mit einer qualifizierten American Express Kredit- oder Debitkarte.", "total": "Gesamt", "continueShopping": "Einkauf fortsetzen", "reserve": "Reservieren", "youAreAllSetToPlayNow": "Sie sind bereit zum Spielen! Gehen Sie zum nächsten Kiosk und scannen Sie den QR-Code, um zu beginnen"}, "BottomSheetComp": {"niceToSeeYou": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> zu <PERSON>hen", "Guest": "Gas<PERSON>"}, "drawer": {"notifications": "Benachrichtigungen", "referFriend": "Freund empfehlen", "myMatches": "<PERSON><PERSON>", "rewards": "Bel<PERSON>nungen", "recycleBalls": "Bälle recyceln", "help": "<PERSON><PERSON><PERSON>", "settings": "Einstellungen", "manageCommunity": "Gemeinschaft verwalten", "orders": "Bestellungen", "assistantCoach": "Assistent-Trainer"}, "assistantCoach": {"welcome": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>-Trainer", "subtitle": "Ihre einzige Quelle für kostenloses Schlägersport-Management"}, "InvitePlayersAssistantCoach": {"title": "Laden Sie Ihre Spieler ein", "playerName": "Spielername", "playerPhone": "Spieler-Telefonnummer", "playerEmail": "Spieler-E-Mail-Adresse", "name": "Name e<PERSON>ben", "phone": "Telefonnummer eingeben", "email": "E-Mail-Ad<PERSON><PERSON>"}, "assistantCoachOptions": {"editProfile": "Trainerprofil bearbeiten", "calendar": "<PERSON><PERSON><PERSON>", "manageClasses": "<PERSON><PERSON> verwalten", "managePlayerAssets": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> ver<PERSON>ten", "manageServiceRequest": "Serviceanfragen verwalten", "manageContent": "Inhalte verwalten", "postAnAd": "<PERSON><PERSON><PERSON> schalten", "orderReserveEquipment": "Ausrüstung bestellen/reservieren", "getCertified": "Zertifizierung erhalten", "messageNotificationPreferences": "Nachrichten- und Benachrichtigungseinstellungen", "invitePlayers": "Laden Sie Ihre Spieler ein", "title": "Assistent-Trainer"}, "drawerNotification": {"notifications": "Benachrichtigungen", "searchNotifications": "Benachrichtigungen suchen", "notificationsWidget": "Benachrichtigungs-Widget", "chatWidget": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "inviteFriendsGet10Off": "Freunde einladen, 10% Rabatt erhalten"}, "drawerReferFriend": {"referFriend": "Freund empfehlen", "searchReferrals": "Empfehlungen suchen", "inviteFriendsGet10Off": "Freunde einladen, 10% Rabatt erhalten", "uploadContacts": "Kontakte hochladen"}, "drawerMyMatches": {"myMatches": "<PERSON><PERSON>", "searchMatches": "<PERSON><PERSON><PERSON> suchen", "calendar": "<PERSON><PERSON><PERSON>", "matchHistory": "Spielhistorie", "notificationsWidget": "Benachrichtigungs-Widget", "chatWidget": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "noMatchesFound": "<PERSON><PERSON> gefunden"}, "balls": {"purchase": "<PERSON><PERSON><PERSON> kaufen", "search": "<PERSON><PERSON><PERSON>en", "help": "Helfen Sie mir bei der Auswahl eines Tennisballs", "filter": "Ergebnisse filtern", "availableNowInKiosk": "Jetzt im Kiosk verfügbar", "age": "Alter"}, "shareScreen": {"needHelp": "Benötigen Sie Hilfe?", "pickup": "Abholung / Rückgabe", "direct": "Direkte Verbindung"}, "CommunityScreen": {"home": "Startseite", "playerConnect": "S<PERSON>ler-Verbindung", "reviews": "Bewertungen", "groups": "Gruppen", "goLife": "GoLife", "upYourGame": "Verbessern Sie Ihr Spiel", "goStream": "GoStream"}, "editCoachProfile": {"title": "Trainerprofil bearbeiten", "addPhoto": "FOTO HINZUFÜGEN", "description": "Profilbeschreibung", "descriptionPlaceholder": "Beschreibung eingeben", "descriptionRequired": "Beschreibung ist erforderlich", "certifications": "Zertifizierungen", "private": "Privat", "locationsAvailable": "<PERSON>erfüg<PERSON><PERSON>", "publicCourts": "Öffentliche Plätze", "sportsClub": "Sportvereine", "videoCoaching": "Video-Coaching", "rate": "<PERSON><PERSON><PERSON>", "otherServices": "Andere Dienstleistungen", "stringing": "Besaitung", "gripping": "<PERSON><PERSON><PERSON>", "customization": "Anpassung", "CoachingSkillsSets": "Trainer-Fähigkeiten", "doubles": "<PERSON><PERSON>", "footwork": "Beinarbeit & Kondition", "funGames": "Spaß & Spiele", "groupLessons": "Gruppenstunden", "mentalSkills": "Mentale Fähigkeiten", "privateLessons": "Privatstunden", "redOrange": "Rot-, Orange- und Grünball-Progression", "singles": "<PERSON><PERSON>", "technicalFundamentals": "Technische Grundlagen", "typesOfPlayers": "Spielertypen", "adult": "Erwachsene", "learning": "<PERSON><PERSON><PERSON> le<PERSON>n", "middleSchool": "Mittelschule", "preSchool": "Vorschule & Kindergarten", "socialPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "youth": "Jugend", "save": "Speichern"}, "calendarScreen": {"title": "<PERSON><PERSON><PERSON>", "newClass": "<PERSON><PERSON><PERSON>", "manageClasses": "<PERSON><PERSON> verwalten", "upcomingClasses": "Kommende Kurse", "today": "<PERSON><PERSON>", "upcoming": "<PERSON><PERSON><PERSON>", "widget": "Widget"}, "manageClassesScreen": {"title": "<PERSON><PERSON> verwalten", "unscheduled": "<PERSON><PERSON> g<PERSON>", "className": "<PERSON><PERSON><PERSON>", "classNamePlaceholder": "Name e<PERSON>ben", "classDescription": "Kursbeschreibung", "classDescriptionPlaceholder": "Beschreibung eingeben", "classDescriptionRequired": "Beschreibung ist erforderlich", "selectDate": "Datum, Uhrzeit und Häufigkeit auswählen", "maximumStudents": "Maximale Anzahl <PERSON>", "numberPlaceholder": "<PERSON><PERSON><PERSON> e<PERSON>", "private": "Privat", "scheduledClasses": "<PERSON><PERSON><PERSON>"}, "managePlayerAssetsScreen": {"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> ver<PERSON>ten", "students": "<PERSON><PERSON><PERSON><PERSON>"}, "manageServiceRequestScreen": {"title": "Serviceanfragen verwalten", "stringing": "Besaitung", "grips": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customs": "Anpassungen"}, "manageContentScreen": {"title": "Inhalte verwalten", "storage": "<PERSON><PERSON><PERSON><PERSON>", "upgrade": "Upgrade", "stats": "Statistiken", "selectAll": "Alle auswählen", "videos": "Videos", "today": "<PERSON><PERSON>", "yesterday": "Gestern"}, "postAnAdScreen": {"title": "<PERSON><PERSON><PERSON> schalten", "classListing": "<PERSON><PERSON><PERSON><PERSON>", "saveDraft": "Entwurf speichern", "className": "<PERSON><PERSON><PERSON>", "classNamePlaceholder": "Name e<PERSON>ben", "classDescription": "Kursbeschreibung", "classDescriptionPlaceholder": "Beschreibung eingeben", "classDescriptionRequired": "Beschreibung ist erforderlich", "selectDate": "Datum, Uhrzeit und Häufigkeit auswählen", "maximumStudents": "Maximale Anzahl <PERSON>", "numberPlaceholder": "<PERSON><PERSON><PERSON> e<PERSON>", "preview": "Vorschau", "post": "Veröffentlichen", "classRequired": "Kurs<PERSON> ist erforderlich"}, "orderReserveEquipmentScreen": {"title": "Ausrüstung bestellen/reservieren", "subTitle": "Ausrüstung reservieren oder kaufen"}, "getCertifiedScreen": {"title": "Zertifizierung erhalten", "recCoachWorkshop": "Freizeit-Trainer-Workshop", "level1": "Level 1", "level2": "Level 2", "level3": "Level 3", "specialtyWorkshops": "Spezial-Workshops", "ptrw": "PTRW"}, "messageNotificationPreferencesScreen": {"title": "Nachrichten- und Benachrichtigungseinstellungen", "pushNotifications": "Push-Benachrichtigungen"}, "communityTabs": {"home": "Startseite", "playerConnect": "S<PERSON>ler-Verbindung", "reviews": "Bewertungen", "groups": "Gruppen", "goLife": "GoLife", "upYourGame": "Verbessern Sie Ihr Spiel", "goStream": "GoStream"}, "findPlayer": {"title": "Spieler finden", "searchPlaceHolder": "Name, Standort, Gruppe usw. suchen", "accept": "Akzeptieren", "findPlayerTabs": {"schedulePlay": "Spiel planen", "nearby": "In der Nähe", "friends": "Freunde", "group": "Gruppe", "invite": "Einladen"}, "invitePlayers": "<PERSON><PERSON><PERSON> einladen", "namePlaceholder": "Name e<PERSON>ben", "emailPlaceholder": "E-Mail-Ad<PERSON><PERSON>", "phonePlaceholder": "Telefonnummer eingeben", "ratingPlaceholder": "Bewertung eingeben (optional)", "nameRequired": "Name ist erforderlich", "emailInvalid": "E-Mail-Format ist ungültig", "phoneRequired": "Telefonnummer ist erforderlich", "name": "Name", "email": "E-Mail", "phoneNumber": "Telefonnummer", "rating": "Bewertung", "searchEvent": "Event suchen", "Results": "Ergebnisse"}, "groupsScreen": {"title": "Gruppen", "createGroup": "Gruppe er<PERSON>llen", "myGroups": "Meine Gruppen", "joinGroup": "Gruppe beitreten", "scoreBoard": "Punktetafel", "allGroups": "Alle Gruppen", "favoriteGroups": "Lieblings-Gruppen"}, "newGroupScreen": {"title": "Gruppe er<PERSON>llen", "newGroup": "Neue Gruppe", "groupNamePlaceholder": "Gruppenname e<PERSON>ben", "groupSettings": "Gruppeneinstellungen", "privacy": "Privatsphäre", "favoriteLocation": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "favoriteLocationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>ben", "tags": "Tags"}, "addMembersScreen": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "addMembers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "Name oder E-Mail suchen", "results": "Ergebnisse", "filterResults": "Ergebnisse filtern", "show": "Anzeigen", "tennis": "Tennis", "pickleball": "Pickleball", "padel": "Pa<PERSON>", "platformTennis": "Platform Tennis", "utrRating": "UTR-Bewertung", "friends": "Freunde", "invited": "Eingeladen", "sponsored": "Gesponsert", "searchEvent": "Event suchen"}, "createGroupMemberListScreen": {"title": "Gruppe er<PERSON>llen", "newGroup": "Neue Gruppe", "groupNamePlaceholder": "Gruppenname e<PERSON>ben", "groupSettings": "Gruppeneinstellungen", "members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "of": "von"}, "myGroupsScreen": {"title": "Meine Gruppen"}, "joinGroupsScreen": {"title": "Gruppe beitreten", "searchPlaceholder": "Name oder E-Mail suchen"}, "joinGroupDetailsScreen": {"title": "Gruppe beitreten", "joinGroup": "Gruppe beitreten"}, "reviewsScreen": {"title": "Neueste Bewertungen", "refineResults": "Ergebnisse verfeinern", "searchPlaceholder": "Bewertungen durchsuchen", "results": "Ergebnisse", "noReviewsFound": "<PERSON><PERSON> Bewertungen gefunden", "noReviewsFoundMessage": "Zurzeit sind keine Bewertungen verfügbar"}, "commentScreen": {"title": "Einen Kommentar schreiben", "rating": "Bewertung", "headline": "Überschrift", "description": "Beschreibung", "addImage": "Bild hinzufügen", "ratingRequired": "Bewertung ist erforderlich", "selectRating": "Bitte wählen Sie eine Bewertung", "headlineRequired": "Überschrift ist erforderlich", "descriptionRequired": "Beschreibung ist erforderlich", "maxCharacters": "Max. 1500 Zeichen", "uploadImagesAndVideos": "Bilder und Videos hochladen", "writeHeadline": "Eine Überschrift schreiben", "describeExperience": "Beschreiben Sie Ihre Erfahrung mit diesem Gerät. Geben Sie an, was gut war und warum, sowie Verbesserungsmöglichkeiten, falls vorhanden. Limit: 1500 Zeichen"}, "BiometricsScreen": {"title": "Biometrische Authentifizierung", "notAvailable": "Biometrische Authentifizierung ist auf Ihrem Gerät nicht verfügbar.", "description": "Verwenden Sie {getBiometryTypeText()}, um Ihre A<PERSON> zu sichern und zu vermeiden, dass Sie bei jedem Öffnen der App Ihr Passwort eingeben müssen.", "biometrics": "Biometrie", "faceId": "Face ID", "touchId": "Touch ID", "enable": "Aktivieren", "whenEnabled": "<PERSON><PERSON>,", "required": " wird bei jedem Öffnen der App erforderlich sein.", "protection": "<PERSON><PERSON><PERSON>", "use": "Verwenden", "toSecure": "um Ihre App zu sichern und zu vermeiden, dass Sie bei jedem Öffnen der App Ihr Passwort eingeben müssen.", "biometricAuthenticationEnabled": "Biometrische Authentifizierung wurde aktiviert", "failedToEnableBiometricAuthentication": "Fehler beim Aktivieren der biometrischen Authentifizierung", "biometricAuthenticationDisabled": "Biometrische Authentifizierung wurde deaktiviert", "failedToDisableBiometricAuthentication": "Fehler beim Deaktivieren der biometrischen Authentifizierung", "errorOccurredWhileUpdatingBiometricSettings": "Ein Fehler ist beim Aktualisieren der biometrischen Einstellungen aufgetreten"}, "NotificationScreen": {"title": "Benachrichtigungen", "notifications": "Benachrichtigungen", "searchNotifications": "Benachrichtigungen suchen", "notificationsWidget": "Benachrichtigungs-Widget", "chatWidget": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "trash": "Papierkorb", "more": "<PERSON><PERSON>", "notificationFunction": "Benachrichtigungsfunktion", "reply": "Antworten", "remindMe": "<PERSON><PERSON><PERSON> mich"}, "equipmentReservationScreen": {"title": "Ausrüstung reservieren oder kaufen", "age": "Alter", "availableNowInKiosk": "Jetzt im Kiosk verfügbar"}}