import React, {useState} from 'react';
import {StyleProp, TouchableOpacity, View, ViewStyle, FlatList} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import Typography from '../Typography';
import Icon from '@/components/CIcon';
import RenderHtml from 'react-native-render-html';
import CImage from '@/components/CImage';
import {Images} from '@/config';

interface CommunityCardProps {
  data: GoFitData;
  onMorePress?: () => void;
  onMoreIconPress?: () => void;
  onLikePress?: () => void;
  onCommentPress?: () => void;
  onSharePress?: () => void;
  onCardPress?: () => void;
  showMore?: boolean;
  variant?: 'default' | 'tag';
  containerStyle?: StyleProp<ViewStyle>;
  key?: string;
}

interface GoFitData {
  title: string;
  description: string;
  image: string;
  file_thumbnail: string;
  tags: GoFitTag[];
}

interface GoFitTag {
  id: number;
  name: string;
  title: string;
  color?: string;
}

const CommunityCard = (props: CommunityCardProps) => {
  const {
    data,
    onMorePress = () => {},
    onMoreIconPress = () => {},
    onLikePress = () => {},
    onCommentPress = () => {},
    onSharePress = () => {},
    onCardPress = () => {},
    showMore = true,
    variant = 'default',
    containerStyle,
    key = '',
  } = props;

  const [isExpanded, setIsExpanded] = useState(false);

  const plainTextDescription = data?.description?.replace(/<[^>]+>/g, '') || '';
  const isLongDescription = plainTextDescription.length > 20;

  const htmlContent = {
    html: data?.description || '',
  };

  const getVisibleHtml = () => {
    if (isExpanded || !isLongDescription) return htmlContent;
    return {
      html: `${plainTextDescription.slice(0, 20)}...`,
    };
  };

  const toggleExpanded = () => setIsExpanded(prev => !prev);

  const theme = useThemeStore();
  const styles = createStyles(theme);

  const tagArray = [
    {id: 1, name: 'primary', title: 'tag', color: theme.colors.richSkyBlue},
    {id: 2, name: 'success', title: 'tag', color: theme.colors.pinkishPurple},
    {id: 3, name: 'error', title: 'tag', color: theme.colors.tangerine},
  ];

  return (
    <TouchableOpacity
      key={key}
      activeOpacity={0.8}
      onPress={onCardPress}
      style={[styles.root, containerStyle]}>
      <View style={styles.contentContainer}>
        <Typography variant="subTitle4" style={styles.title}>
          {data?.title}
        </Typography>
        <View style={isExpanded ? styles.descriptionContainerexpand : styles.descriptionContainer}>
          <RenderHtml contentWidth={20} source={getVisibleHtml()} baseStyle={styles.description} />
          {isLongDescription && (
            <TouchableOpacity onPress={toggleExpanded}>
              <Typography variant="moreText" style={styles.moreText}>
                {isExpanded ? 'less <<' : 'more >>'}
              </Typography>
            </TouchableOpacity>
          )}
        </View>

        <View>
          {variant === 'tag' && (
            <View style={styles.tagsContainer}>
              {data?.tags?.length > 0 && (
                <FlatList
                  data={data?.tags || []}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  keyExtractor={item => item.id.toString()}
                  contentContainerStyle={{gap: 7}}
                  renderItem={({item}) => (
                    <TouchableOpacity
                      activeOpacity={0.8}
                      style={[
                        styles.tag,
                        {
                          backgroundColor:
                            item?.color ||
                            tagArray.find(t => t.id === item.id)?.color ||
                            theme.colors.richSkyBlue,
                        },
                      ]}>
                      <Typography variant="tagTitle" color={theme.colors.white}>
                        {item?.name}
                      </Typography>
                    </TouchableOpacity>
                  )}
                />
              )}
              <TouchableOpacity onPress={onMoreIconPress} style={styles.moreIconContainer}>
                <Icon name="threeDot" color={theme.colors.white} size={18} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
      <View style={styles.thumbnailContainer}>
        <CImage
          source={
            data?.file_thumbnail && data?.file_thumbnail?.startsWith('http')
              ? {uri: data?.file_thumbnail}
              : Images.thumbnail
          }
          style={styles.thumbnail}
        />
      </View>
    </TouchableOpacity>
  );
};

export default CommunityCard;
