import React, {useState, useEffect, useCallback, useMemo, useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, TouchableOpacity, ActivityIndicator, Keyboard, TextInput} from 'react-native';
import ParkCard from '../ParkCard';
import {styles as createStyles} from './styles';
import {BottomSheetFlatList, BottomSheetTextInput} from '@gorhom/bottom-sheet';
import {CButton, CImage, CInput, Icon} from '@/components';
import {useNavigation} from '@react-navigation/native';
import Typography from '../Typography';
import {Images} from '@/config';
import {useAuthStore} from '@/store';
import {MapPin, mapPins} from '@/data';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import useTranslation from '@/hooks/useTranslation';
import SearchInput from '../SearchInput';

interface ParkData {
  id: string;
  title: string;
  description: string;
  coordinate: {
    latitude: string;
    longitude: string;
  };
}

interface FormData {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

const ParksSearchScreen = ({
  onParkSelect,
  type,
  onBackPress,
}: {
  onParkSelect: (parkData: ParkData) => void;
  type?: string;
  onBackPress?: () => void;
}): React.ReactElement => {
  const navigation = useNavigation<any>();
  const theme = useThemeStore();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const {user} = useAuthStore();
  const {t} = useTranslation();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [visibleParks, setVisibleParks] = useState<MapPin[]>(mapPins);
  const [pagination, setPagination] = useState({page: 1, loadMore: false});
  const [paginationLoader, setPaginationLoader] = useState(false);
  const [recommendCollapsed, setRecommendCollapsed] = useState(true);
  const [showThankYou, setShowThankYou] = useState(false);

  const nameInputRef = useRef<TextInput>(null);
  const addressInputRef = useRef<TextInput>(null);
  const cityInputRef = useRef<TextInput>(null);
  const stateInputRef = useRef<TextInput>(null);
  const zipInputRef = useRef<TextInput>(null);

  const handleParkSelect = useCallback(
    (park: MapPin) => {
      navigation.navigate('DateScreen', {parkData: park});
    },
    [navigation],
  );

  const handleEndReached = useCallback(() => {
    if (pagination.loadMore && !paginationLoader) {
      console.log('Setting loader to true');
      setPaginationLoader(true);
      const nextPage = pagination.page + 1;

      setTimeout(() => {
        console.log('Setting loader to false');
        setPagination(prev => ({...prev, page: nextPage}));
        setPaginationLoader(false);
        setPagination({page: nextPage, loadMore: false});
      }, 3000);
    }
  }, [pagination, paginationLoader]);

  const renderFooter = useCallback(
    () =>
      paginationLoader ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Typography variant="storageTag" style={styles.loaderText}>
            {t('ParksSearchScreen.loadingMoreParks')}
          </Typography>
        </View>
      ) : null,
    [paginationLoader, styles, theme.colors.primary],
  );

  const clearSearch = () => setSearchQuery('');

  const handleSearch = useCallback((query: string) => {
    const filtered = query
      ? mapPins.filter(
          park =>
            park.title.toLowerCase().includes(query.toLowerCase()) ||
            park.description.toLowerCase().includes(query.toLowerCase()),
        )
      : mapPins;

    setVisibleParks(filtered);
    setPagination({page: 1, loadMore: false});
  }, []);

  const renderItem = useCallback(
    ({item}: {item: MapPin}) => (
      <ParkCard
        parkData={item}
        onSelect={() => {
          if (type === 'playerConnect') {
            onParkSelect(item as unknown as ParkData);
          } else {
            handleParkSelect(item);
          }
        }}
      />
    ),
    [handleParkSelect, type, onParkSelect],
  );

  const schema = yup.object({
    name: yup
      .string()
      .required(t('ParksSearchScreen.nameRequired'))
      .matches(/^[A-Za-z\s]*$/, t('ParksSearchScreen.nameInvalid')),
    address: yup.string().required(t('ParksSearchScreen.addressRequired')),
    city: yup
      .string()
      .required(t('ParksSearchScreen.cityRequired'))
      .matches(/^[A-Za-z\s]*$/, t('ParksSearchScreen.cityInvalid')),
    state: yup
      .string()
      .required(t('ParksSearchScreen.stateRequired'))
      .matches(/^[A-Za-z\s]*$/, t('ParksSearchScreen.stateInvalid')),
    zipCode: yup
      .string()
      .required(t('ParksSearchScreen.zipCodeRequired'))
      .matches(/^[0-9]+$/, t('ParksSearchScreen.invalidZipCode')),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      name: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
    },
  });

  const onSubmit = (data: FormData) => {
    Keyboard.dismiss();
    reset();
    setRecommendCollapsed(true);
    setShowThankYou(true);
  };

  return (
    <React.Fragment>
      {type === 'playerConnect' && (
        <View style={styles.backButtonContainer}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.backButton}
            onPress={() => {
              if (onBackPress) {
                onBackPress();
              }
            }}>
            <Icon name="back" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.headerContainer}>
        <CImage source={Images.coachProfile} style={styles.headerImage} resizeMode="contain" />
        <Typography variant="title2" style={{color: theme.colors.text}}>
          {t('ParksSearchScreen.niceToSeeYou')} {user?.name || t('ParksSearchScreen.guest')}
        </Typography>
      </View>

      <SearchInput
        placeholder={t('ParksSearchScreen.whereDoYouWantToPlay')}
        value={searchQuery}
        onChangeText={setSearchQuery}
        containerStyle={styles.searchContainer}
        onClear={clearSearch}
        type="bottomSheetSearch"
        variant="light"
        onSearch={handleSearch}
        debounceTime={300}
      />

      <BottomSheetFlatList
        data={visibleParks}
        renderItem={renderItem}
        keyExtractor={(item: MapPin) => item.id}
        contentContainerStyle={styles.parkList}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Typography variant="emptyCourt" style={styles.emptyText}>
              {t('ParksSearchScreen.noKiosk')}
            </Typography>
            <View style={styles.recommendView}>
              {recommendCollapsed ? (
                <TouchableOpacity onPress={() => setRecommendCollapsed(false)}>
                  <Typography
                    variant="permissionTitle"
                    color={theme.colors.text}
                    style={styles.recommendTitle}>
                    {t('ParksSearchScreen.recommendLocation')}
                  </Typography>
                  {showThankYou && (
                    <Typography variant="permissionTitle" color={theme.colors.text}>
                      Thank you.
                    </Typography>
                  )}
                  <View style={{alignItems: 'center'}}>
                    <Icon name="dropdown" size={18} color={theme.colors.text} />
                  </View>
                </TouchableOpacity>
              ) : (
                <View>
                  <Typography
                    variant="permissionTitle"
                    color={theme.colors.text}
                    style={{
                      marginBottom: 16,
                    }}>
                    {t('ParksSearchScreen.recommendLocation')}
                  </Typography>

                  <Typography
                    variant="emptyCourt"
                    color={theme.colors.text}
                    style={{marginBottom: 10}}>
                    {t('ParksSearchScreen.enter')}
                  </Typography>
                  <View>
                    <View style={styles.inputContainer}>
                      <Controller
                        control={control}
                        name="name"
                        render={({field: {onChange, onBlur, value}}) => (
                          <CInput
                            label={t('profile.name')}
                            showLabel={false}
                            placeholder={t('profile.name')}
                            variant="light"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                            hasError={!!errors.name}
                            error={errors.name?.message}
                            inputStyle={styles.input}
                            containerStyle={{marginBottom: 0}}
                            ref={nameInputRef}
                            returnKeyType="next"
                            onSubmitEditing={() => addressInputRef.current?.focus()}
                            blurOnSubmit={false}
                            useBottomSheetInput={true}
                          />
                        )}
                      />
                    </View>
                    <View style={styles.inputContainer}>
                      <Controller
                        control={control}
                        name="address"
                        render={({field: {onChange, onBlur, value}}) => (
                          <CInput
                            label="Address"
                            showLabel={false}
                            variant="light"
                            placeholder="Address"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                            hasError={!!errors.address}
                            error={errors.address?.message}
                            inputStyle={styles.input}
                            containerStyle={{marginBottom: 0}}
                            ref={addressInputRef}
                            returnKeyType="next"
                            onSubmitEditing={() => cityInputRef.current?.focus()}
                            blurOnSubmit={false}
                            useBottomSheetInput={true}
                          />
                        )}
                      />
                    </View>
                    <View style={styles.recommendInputContainer}>
                      <View style={[styles.inputContainer, {flex: 1}]}>
                        <Controller
                          control={control}
                          name="city"
                          render={({field: {onChange, onBlur, value}}) => (
                            <CInput
                              variant="light"
                              label={t('ParksSearchScreen.city')}
                              showLabel={false}
                              placeholder={t('ParksSearchScreen.city')}
                              value={value}
                              onChangeText={onChange}
                              onBlur={onBlur}
                              hasError={!!errors.city}
                              error={errors.city?.message}
                              inputStyle={styles.input}
                              containerStyle={{marginBottom: 0}}
                              ref={cityInputRef}
                              returnKeyType="next"
                              onSubmitEditing={() => stateInputRef.current?.focus()}
                              blurOnSubmit={false}
                              useBottomSheetInput={true}
                            />
                          )}
                        />
                      </View>
                      <View style={{flex: 1, flexDirection: 'row', gap: 8}}>
                        <View style={[styles.inputContainer, {flex: 1}]}>
                          <Controller
                            control={control}
                            name="state"
                            render={({field: {onChange, onBlur, value}}) => (
                              <CInput
                                variant="light"
                                label={t('ParksSearchScreen.state')}
                                showLabel={false}
                                placeholder={t('ParksSearchScreen.state')}
                                value={value}
                                onChangeText={onChange}
                                onBlur={onBlur}
                                hasError={!!errors.state}
                                error={errors.state?.message}
                                inputStyle={styles.input}
                                containerStyle={{marginBottom: 0}}
                                ref={stateInputRef}
                                returnKeyType="next"
                                onSubmitEditing={() => handleSubmit(onSubmit)}
                                blurOnSubmit={false}
                                useBottomSheetInput={true}
                              />
                            )}
                          />
                        </View>
                        <View style={[styles.inputContainer, {flex: 1}]}>
                          <Controller
                            control={control}
                            name="zipCode"
                            render={({field: {onChange, onBlur, value}}) => (
                              <CInput
                                variant="light"
                                label={t('ParksSearchScreen.zipCode')}
                                showLabel={false}
                                placeholder={t('ParksSearchScreen.zipCode')}
                                value={value}
                                onChangeText={onChange}
                                onBlur={onBlur}
                                hasError={!!errors.zipCode}
                                error={errors.zipCode?.message}
                                inputStyle={styles.input}
                                containerStyle={{marginBottom: 0}}
                                ref={zipInputRef}
                                returnKeyType="next"
                                onSubmitEditing={() => handleSubmit(onSubmit)}
                                blurOnSubmit={false}
                                useBottomSheetInput={true}
                                keyboardType="numeric"
                                maxLength={5}
                              />
                            )}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                  <CButton
                    title={t('ParksSearchScreen.submit')}
                    onPress={handleSubmit(onSubmit)}
                    containerStyle={{height: 47}}
                    textStyle={{lineHeight: 18}}
                  />
                  <TouchableOpacity
                    onPress={() => setRecommendCollapsed(true)}
                    activeOpacity={0.7}
                    style={{justifyContent: 'center', alignItems: 'center', paddingTop: 10}}>
                    <Icon
                      name="dropdown"
                      size={18}
                      color={theme.colors.text}
                      style={{transform: [{rotate: '180deg'}]}}
                    />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        }
        showsVerticalScrollIndicator={false}
        scrollEnabled
        nestedScrollEnabled
        keyboardShouldPersistTaps="handled"
      />
    </React.Fragment>
  );
};

export default ParksSearchScreen;
