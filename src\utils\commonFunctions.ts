import api from '@/services/api';
import {Alert} from 'react-native';
import {QueryClient} from '@tanstack/react-query';
import Toast, {ToastPosition} from 'react-native-toast-message';

export const getCommunityData = async (type = '', itemType = '') => {
  try {
    const response = await api.get(`/community-cruds/list?type=${type}&item_type=${itemType}`);
    if (response.status) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

export const updateCommunityLike = async (data: {
  id: string;
  is_liked: boolean;
  comment?: string;
}) => {
  try {
    const response = await api.post('/community-cruds/like-comment', {
      community_crud_id: data.id,
      is_liked: data.is_liked,
      comment: data.comment || '',
    });
    return response.data;
  } catch (error) {
    console.error('Error updating like:', error);
    throw error;
  }
};

interface LikeableItem {
  id: string;
  is_liked: boolean;
  likes_count: number;
}

export const setupCommunityMutation = <T extends LikeableItem>(
  queryClient: QueryClient,
  queryKey: string[],
  updateStateCallback?: (id: string, isLiked: boolean) => void,
) => {
  return {
    mutationFn: async (data: {id: string; is_liked: boolean; comment?: string}) => {
      return updateCommunityLike(data);
    },
    onMutate: async (newData: {id: string; is_liked: boolean}) => {
      await queryClient.cancelQueries({queryKey});
      const previousData = queryClient.getQueryData(queryKey);

      // Update the data in the query cache
      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old || !old.data) return old;

        const updatedData = {
          ...old,
          data: old.data.map((item: T) =>
            item.id === newData.id
              ? {
                  ...item,
                  is_liked: newData.is_liked,
                  likes_count: newData.is_liked
                    ? (item.likes_count || 0) + 1
                    : (item.likes_count || 0) - 1,
                }
              : item,
          ),
        };
        return updatedData;
      });

      // Call the state update callback if provided
      if (updateStateCallback) {
        updateStateCallback(newData.id, newData.is_liked);
      }

      return {previousData};
    },
    onError: (err: Error, newData: {id: string; is_liked: boolean}, context: any) => {
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({queryKey});
    },
  };
};

export const fetchQRGenerator = async () => {
  try {
    const response = await api.get('/user/get-qr-token');
    return response.data.data;
  } catch (error) {
    toaster('error', 'Failed to generate QR code');
    console.error(error);
    return null;
  }
};

interface PasswordValidation {
  isValid: boolean;
  message: string;
  details: {
    length: boolean;
    mix: boolean;
    upperLower: boolean;
  };
}

export const validatePassword = (password: string): PasswordValidation => {
  const length = password.length >= 8;
  const mix = /[A-Za-z]/.test(password) && /\d/.test(password) && /[~!@#$%^*?_\-+=]/.test(password);
  const upperLower = /[A-Z]/.test(password) && /[a-z]/.test(password);
  const isValid = length && mix && upperLower;

  const message = isValid
    ? 'Password meets all requirements'
    : 'Password must have at least 8 characters, including uppercase, lowercase, a number, and a symbol';

  return {
    isValid,
    message,
    details: {
      length,
      mix,
      upperLower,
    },
  };
};

export const toaster = (type = '', text1 = '', position = 'top', text2 = '') => {
  Toast.show({
    type: type,
    text1: text1,
    position: position as ToastPosition,
    text2: text2,
  });
};
