import React from 'react';
import {Text, StyleSheet, TouchableOpacity, Alert} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';

const GestureTestScreen = () => {
  const navigation = useNavigation();
  const theme = useThemeStore();

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .failOffsetX([-Number.MAX_SAFE_INTEGER, -15])
    .failOffsetY([-20, 20])
    .onStart(() => {
      console.log('Gesture started');
    })
    .onUpdate((event) => {
      console.log('Gesture update:', event.translationX, event.velocityX);
    })
    .onEnd((event) => {
      console.log('Gesture ended:', event.translationX, event.velocityX);
      const shouldGoBack = event.translationX > 80 && event.velocityX > 0;
      
      if (shouldGoBack && navigation.canGoBack()) {
        Alert.alert('Gesture Detected!', 'Swipe back gesture triggered', [
          {text: 'Go Back', onPress: () => navigation.goBack()},
          {text: 'Stay', style: 'cancel'},
        ]);
      }
    });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.primary,
      textAlign: 'center',
      marginBottom: 30,
    },
    instruction: {
      fontSize: 18,
      color: theme.colors.white,
      textAlign: 'center',
      marginBottom: 20,
      lineHeight: 26,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 15,
      borderRadius: 8,
      marginTop: 30,
      minWidth: 200,
    },
    buttonText: {
      color: theme.colors.black,
      fontSize: 16,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    highlight: {
      color: theme.colors.primary,
      fontWeight: 'bold',
    },
  });

  return (
    <GestureDetector gesture={panGesture}>
      <SafeAreaView style={styles.container}>
        <Text style={styles.title}>🔧 Gesture Debug Test</Text>
        
        <Text style={styles.instruction}>
          This screen tests the <Text style={styles.highlight}>raw gesture detection</Text>
        </Text>
        
        <Text style={styles.instruction}>
          👆 Swipe right anywhere on the screen
        </Text>
        
        <Text style={styles.instruction}>
          Check console logs and alert popup
        </Text>
        
        <Text style={styles.instruction}>
          If gesture works, you'll see an alert asking to go back
        </Text>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.buttonText}>Go Back (Button)</Text>
        </TouchableOpacity>
      </SafeAreaView>
    </GestureDetector>
  );
};

export default GestureTestScreen;
