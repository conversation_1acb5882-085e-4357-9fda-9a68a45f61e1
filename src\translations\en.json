{"//": "English (English) translations for the application", "common": {"ok": "OK", "cancel": "Cancel", "back": "Back", "next": "Next", "save": "Save", "delete": "Delete", "edit": "Edit", "loading": "Loading...", "error": "Error", "success": "Success", "search": "Search", "SEARCH": "SEARCH", "community": "COMMUNITY", "kiosk": "KIOSK", "filter": "Filter", "apply": "Apply", "reset": "Reset", "close": "Close", "upload": "Upload", "invite": "Invite", "uploadContacts": "Upload contacts", "enterName": "Enter a name", "enterEmail": "Enter an email address", "enterPhone": "Enter a Phone number", "enterAtpRating": "Enter ATP rating (Optional)", "on": "ON", "off": "OFF", "create": "Create", "public": "Public", "private": "Private", "hidden": "Hidden", "skip": "<PERSON><PERSON>", "age": "Age", "leaveComment": "Leave a comment", "likeVideo": "Like this video", "shareVideo": "Share this video", "reportAbuse": "Report abuse", "inviteMore": "Invite more!", "inviteSent": "In<PERSON><PERSON> has been sent!"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "loginWithEmail": "Login with <PERSON><PERSON>", "loginWithGoogle": "Login with Google", "loginWithFacebook": "Login with Facebook", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "logout": "Logout", "verification": "Verification"}, "profile": {"profile": "Profile", "settings": "Settings", "personalInfo": "Personal Information", "name": "Name", "phoneNumber": "Phone Number", "bio": "Bio", "updateProfile": "Update Profile", "createProfile": "Create Profile", "whichBestDescribesYou": "Which best describes you?"}, "settings": {"settings": "Settings", "language": "Language", "notifications": "Notifications", "darkMode": "Dark Mode", "biometrics": "Biometrics", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "shareLocationWithFriends": "Share Location with Friends", "twoFactorAuthentication": "Two-factor authentication", "about": "About", "version": "Version", "searchSettings": "Search Settings"}, "booking": {"searchParks": "Search Parks", "selectDateTime": "Select Date & Time", "purchaseEquipments": "Purchase Equipment", "cartView": "<PERSON><PERSON>", "createProfile": "Create Profile", "signup": "Sign Up", "advertisement": "Advertisement", "subscription": "Subscription"}, "parks": {"findParks": "Find Parks", "nearbyParks": "Nearby Parks", "location": "Location", "distance": "Distance", "facilities": "Facilities", "courts": "Courts", "availableNow": "Available Now"}, "equipment": {"equipment": "Equipment", "racquets": "RACQUETS", "balls": "BALLS", "accessories": "Accessories", "brand": "Brand", "price": "Price", "addToCart": "Add to Cart", "removeFromCart": "Remove from Cart"}, "permissions": {"locationPermissions": "Location Permissions", "locationDescription": "This app requires location services", "enableLocation": "Enable Location", "enableBluetooth": "Enable Bluetooth", "enableNotifications": "Enable Notifications", "noThanks": "No Thanks", "bluetoothPermissions": "Bluetooth Permissions", "bluetoothDescription": "Works best when bluetooth is enabled", "notificationPermissions": "Notification Permissions", "notificationDescription": "Works best when notifications are enabled"}, "terms": {"title": "Terms and Conditions", "accept": "Accept"}, "signupScreen": {"signUpWith": "Sign up with", "facebook": "Facebook", "google": "Google", "email": "Email", "alreadyHaveAnAccount": "Already have an account?", "login": "<PERSON><PERSON>", "bySigningUpYouAgreeToOur": "By signing up you agree to our", "termsAndConditions": "Terms and Conditions", "signin": "Sign in"}, "login": {"title": "Sign in", "enterYourEmail": "Enter your email", "emailAddress": "Email address", "enterYourPassword": "Enter your password", "password": "Password", "forgotYourPassword": "Forgot your password?", "signUp": "Sign Up", "submit": "Next", "dontHaveAccount": "Don't have an account?"}, "createProfile": {"createProfileTitle": "Create your Goraqt Profile", "name": "Display name", "namePlaceholder": "Enter display name", "nameError": "Name is required", "email": "Email", "emailPlaceholder": "Enter an email address", "emailError": "Email is required", "emailFormatError": "Email format is invalid", "randomNameGenerator": "Generated display names", "age": "Age", "agePlaceholder": "Enter age", "ageError": "Age is required", "ageNumberError": "Age must be a number", "ageLengthError": "Age must be between 13 and 120", "ageFormatError": "Age format is invalid", "userTypeError": "Please select an option", "fitnessLevelError": "Please select a fitness level", "describe": "Describe yourself", "describeFitness": "Which best describes fitness/ability level?", "submitBtn": "Complete profile", "setupLaterBtn": "Setup later", "birthYear": "Birth year", "birthYearPlaceholder": "Enter birth year", "birthYearformatErr": "Must be a valid year (YYYY)", "birthYearRangeErr": "Birth year cannot be greater than current year and should not be more than 60 years ago", "birthYearRequired": "Birth year is required"}, "advertisement": {"title": "Try 30 days of Wilson racquets for $25", "subTitle": "You’ll get 1 hour of play per day for 30 days, 50% off a can of Wilson balls and a $20 credit toward racquet purchase", "skipBtn": "<PERSON><PERSON>", "claimOfferBtn": "Claim offer"}, "subscription": {"trial": "30 day trial", "price": "$20.44", "promoMessage": "After your free trial duration get 10% off membership with an eligible American Express credit or debit card.", "bulletTitle": "Tout copy bullet", "bulletSubtitle": "adipiscing elit, sed do eiusmod tempor", "promoCodePlaceholder": "ENTER PROMO CODE", "termsText": "By purchasing Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud", "termsLink": "Subject to Terms and Conditions", "acceptBtn": "Accept"}, "createProfileLogin": {"title": "Hello", "subTitle": "Just need a few more details to set you up.", "birthYear": "Birth year", "birthYearPlaceholder": "Enter birth year", "ageRequired": "Birth year is required", "userTypeRequired": "Please select an option", "fitnessLevelRequired": "Please select a fitness level", "userType": "Which best describes you?", "fitnessLevel": "Which best describes fitness/ability level?", "completeProfile": "Complete profile", "setupLater": "Setup later"}, "ParksSearchScreen": {"niceToSeeYou": "Nice to see you", "guest": "Guest", "whereDoYouWantToPlay": "Where do you want to play?", "noParksFound": "No Parks Found", "loadingMoreParks": "Loading more parks...", "noKiosk": "There is no smart kiosk in this area.", "recommendLocation": "Recommend a location", "enter": "Please enter", "address": "Address", "city": "City", "state": "State", "submit": "Submit", "zipCode": "Zip code", "nameRequired": "name is required", "nameInvalid": "Name can only contain letters", "addressRequired": "address is required", "cityRequired": "city is required", "cityInvalid": "City can only contain letters", "stateRequired": "state is required", "stateInvalid": "State can only contain letters", "zipCodeRequired": "zip code is required", "invalidZipCode": "Invalid zip code"}, "dateScreen": {"selectDateAndTime": "Select a date and time"}, "datePicker": {"selectDateAndTime": "Select a date and time"}, "customDatePicker": {"addPlayers": "Add Players", "otherPlayers": "Other Players"}, "invitePlayers": {"invitePlayers": "Invite Players", "accept": "Accept", "friends": "Friends", "groups": "Groups", "nearby": "Nearby", "invite,": "Invite", "searchNameLocationGroup": "Search Name, Location, Group etc.", "filterResults": "Filter results", "reserveGear": "Reserve Gear", "inviteSent": "In<PERSON><PERSON> has been sent!", "close": "Close", "invitePlayerTabs": {"friends": "Friends", "groups": "Groups", "nearby": "Nearby", "invite": "Invite"}}, "ShoppingCartSection": {"yourCart": "Your Cart", "total": "Total", "proceedToCheckout": "Proceed to Checkout", "emptyCart": "Your cart is empty", "findAPark": "Find a Park"}, "EquipmentReservation": {"filterResults": "Filter results", "availableNowInKiosk": "Available now in kiosk", "age": "Age", "apply": "Apply", "previous": "Previous", "next": "Next", "reserveOrPurchaseEquipment": "Reserve or purchase equipment"}, "EquipmentDetailModal": {"tryItNow": "TRY IT NOW!", "specifications": "Specifications", "averageRating": "Average Rating", "playerReviews": "Player Reviews", "more": "More", "less": "Less", "staffReviews": "Staff Reviews"}, "RacquetsScreen": {"topBrands": "Top brands", "allBrands": "All brands", "sports": "Sports", "services": "Services"}, "RacquetCategoryScreen": {"byBrand": "By brand", "bestForMyGame": "Best for my game"}, "RacquetsBrandsScreen": {"brands": "Brands"}, "RacquetSelector": {"racquetSelector": "<PERSON><PERSON><PERSON> Selector", "howLongHaveYouBeenPlayingTennis": "How long have you\nbeen playing tennis?", "lessThan2Years": "0-2 years", "moreThan2Years": "2+ years", "whichBestDescribesYourFitnessLevel": "Which best describes\nyour fitness level?", "slowAndSteadyOnTheTreadmill": "Slow and steady\non the treadmill", "workoutWarrior": "Workout\nWarrior", "whichCharacteristicsDoYouMostWantInARacquet": "Which characteristics do you\nmost want in a racquet?", "comfortableAndArmFriendly": "Comfortable\nand arm-friendly", "lotsOfPower": "Lots\nof power"}, "RacquetSelectorDetail": {"RacquetSelectorDetail": "<PERSON><PERSON><PERSON> Selector", "tryItNow": "TRY IT NOW!", "results": "Results"}, "CartScreen": {"Subscriber": "Subscriber", "cart": "<PERSON><PERSON>", "description": "After your free trial duration get 10% off membership with an eligible American Express credit or debit card.", "total": "Total", "continueShopping": "Continue shopping", "reserve": "Reserve", "youAreAllSetToPlayNow": "You are all set to play now! Walk to the nearest kiosk and scan the QR code to start playing"}, "BottomSheetComp": {"niceToSeeYou": "Nice to see yoU", "Guest": "Guest"}, "drawer": {"notifications": "Notifications", "referFriend": "Refer a friend", "myMatches": "My Matches", "rewards": "Rewards", "recycleBalls": "RecycleBalls", "help": "Help", "settings": "Settings", "manageCommunity": "Manage Community", "orders": "Orders", "assistantCoach": "Assistant Coach"}, "assistantCoach": {"welcome": "Welcome to Assistant Coach", "subtitle": "Your single source for free racquet sport management"}, "InvitePlayersAssistantCoach": {"title": "Invite your players", "playerName": "Player Name", "playerPhone": "Player phone number", "playerEmail": "Player email address", "name": "Enter name", "phone": "Enter phone number", "email": "Enter email address"}, "assistantCoachOptions": {"editProfile": "Edit coaching profile", "calendar": "Calendar", "manageClasses": "Manage classes", "managePlayerAssets": "Manage player assets", "manageServiceRequest": "Manage service request", "manageContent": "Manage content", "postAnAd": "Post an ad", "orderReserveEquipment": "Order/Reserve equipment", "getCertified": "Get certified", "messageNotificationPreferences": "Message and  notification preferences", "invitePlayers": "Invite your players", "title": "Assistant Coach"}, "drawerNotification": {"notifications": "Notifications", "searchNotifications": "Search notifications", "notificationsWidget": "Notifications widget", "chatWidget": "Chat widget", "inviteFriendsGet10Off": "Invite friends, get 10% off"}, "drawerReferFriend": {"referFriend": "Refer a friend", "searchReferrals": "Search Referrals", "inviteFriendsGet10Off": "Invite friends, get 10% off", "uploadContacts": "Upload contacts"}, "drawerMyMatches": {"myMatches": "My Matches", "searchMatches": "Search Matches", "calendar": "Calendar", "matchHistory": "Match History", "notificationsWidget": "Notifications widget", "chatWidget": "Chat widget", "noMatchesFound": "No matches found"}, "balls": {"purchase": "Purchase balls", "search": "Search balls", "help": "Help me choose a tennis ball", "filter": "Filter results", "availableNowInKiosk": "Available now in kiosk", "age": "Age"}, "shareScreen": {"needHelp": "Need help?", "pickup": "Pickup / Return", "direct": "Direct Connect"}, "CommunityScreen": {"home": "Home", "playerConnect": "Player Connect", "reviews": "Reviews", "groups": "Groups", "goLife": "GoLife", "upYourGame": "UpYourGame", "goStream": "GoStream"}, "editCoachProfile": {"title": "Edit coaching profile", "addPhoto": "ADD PHOTO", "description": "Profile Description", "descriptionPlaceholder": "Enter description", "descriptionRequired": "Description is required", "certifications": "Certifications", "private": "Private", "locationsAvailable": "Locations available", "publicCourts": "Public Courts", "sportsClub": "Sports Clubs", "videoCoaching": "Video coaching", "rate": "Rate", "otherServices": "Other Services", "stringing": "Stringing", "gripping": "Gripping", "customization": "Customization", "CoachingSkillsSets": "Coaching skill sets", "doubles": "Doubles", "footwork": "Footwork & Conditioning", "funGames": "Fun & Games", "groupLessons": "Group Lessons", "mentalSkills": "Mental Skills", "privateLessons": "Private Lessons", "redOrange": "Red, Orange and Green Ball Progression", "singles": "Singles", "technicalFundamentals": "Technical Fundamentals", "typesOfPlayers": "Types of players", "adult": "Adult", "learning": "Learning How to play", "middleSchool": "Middle School", "preSchool": "Pre-School & Kindergarten", "socialPlayer": "Social Player", "youth": "Youth", "save": "Save"}, "calendarScreen": {"title": "Calendar", "newClass": "New Class", "manageClasses": "Manage Classes", "upcomingClasses": "Upcoming Classes", "today": "Today", "upcoming": "Upcoming", "widget": "Widget"}, "manageClassesScreen": {"title": "Manage Classes", "unscheduled": "Unscheduled", "className": "Class Name", "classNamePlaceholder": "Enter name", "classDescription": "Class description", "classDescriptionPlaceholder": "Enter description", "classDescriptionRequired": "Description is required", "selectDate": "Select date, time and frequency", "maximumStudents": "Maximum number of students", "numberPlaceholder": "Enter number", "private": "Private", "scheduledClasses": "Scheduled Classes"}, "managePlayerAssetsScreen": {"title": "Manage Player Assets", "students": "Students"}, "manageServiceRequestScreen": {"title": "Manage Service Request", "stringing": "Stringing", "grips": "Grips", "customs": "Customs"}, "manageContentScreen": {"title": "Manage Content", "storage": "Storage", "upgrade": "Upgrade", "stats": "Stats", "selectAll": "Select all", "videos": "Videos", "today": "Today", "yesterday": "Yesterday"}, "postAnAdScreen": {"title": "Post an Ad", "classListing": "Class listing", "saveDraft": "Save draft", "className": "Class Name", "classNamePlaceholder": "Enter name", "classDescription": "Class description", "classDescriptionPlaceholder": "Enter description", "classDescriptionRequired": "Description is required", "selectDate": "Select date, time and frequency", "maximumStudents": "Maximum number of students", "numberPlaceholder": "Enter number", "preview": "Preview", "post": "Post", "classRequired": "Class name is required"}, "orderReserveEquipmentScreen": {"title": "Order/Reserve equipment", "subTitle": "Reserve or purchase equipment"}, "getCertifiedScreen": {"title": "Get certified", "recCoachWorkshop": "Rec Coach Workshop", "level1": "Level 1", "level2": "Level 2", "level3": "Level 3", "specialtyWorkshops": "Specialty Workshops", "ptrw": "PTRW"}, "messageNotificationPreferencesScreen": {"title": "Messages and Notifications Preferences", "pushNotifications": "Push notifications"}, "communityTabs": {"home": "Home", "playerConnect": "Player Connect", "reviews": "Reviews", "groups": "Groups", "goLife": "GoLife", "upYourGame": "UpYourGame", "goStream": "GoStream"}, "findPlayer": {"title": "Find Players", "searchPlaceHolder": "Search Name, Location, Group etc.", "accept": "Accept", "findPlayerTabs": {"schedulePlay": "Schedule Play", "nearby": "Nearby", "friends": "Friends", "group": "Group", "invite": "Invite"}, "invitePlayers": "Invite Players", "namePlaceholder": "Enter a name", "emailPlaceholder": "Enter an email address", "phonePlaceholder": "Enter a phone number", "ratingPlaceholder": "Enter rating (Optional)", "nameRequired": "Name is required", "emailInvalid": "Email format is invalid", "phoneRequired": "Phone number is required", "name": "Name", "email": "Email", "phoneNumber": "PhoneNumber", "rating": "Rating", "searchEvent": "Search Event", "Results": "Results"}, "groupsScreen": {"title": "Groups", "createGroup": "Create Group", "myGroups": "My Groups", "joinGroup": "Join Group", "scoreBoard": "Scoreboard", "allGroups": "All Groups", "favoriteGroups": "Favorite Groups"}, "newGroupScreen": {"title": "Create Group", "newGroup": "New Group", "groupNamePlaceholder": "Enter Group Name", "groupSettings": "Group Settings", "privacy": "Privacy", "favoriteLocation": "Favorite Playing Location", "favoriteLocationPlaceholder": "Enter Favorite", "tags": "Tags"}, "addMembersScreen": {"title": "Add Members", "search": "Search", "addMembers": "Add Members", "searchPlaceholder": "Search Name or email", "results": "Results", "filterResults": "Filter results", "show": "Show", "tennis": "Tennis", "pickleball": "Pickleball", "padel": "Pa<PERSON>", "platformTennis": "Platform Tennis", "utrRating": "UTR Rating", "friends": "Friends", "invited": "Invited", "sponsored": "Sponsored", "searchEvent": "Search Event"}, "createGroupMemberListScreen": {"title": "Create Group", "newGroup": "New Group", "groupNamePlaceholder": "Enter Group Name", "groupSettings": "Group Settings", "members": "Members", "of": "of"}, "myGroupsScreen": {"title": "My Groups"}, "joinGroupsScreen": {"title": "Join Group", "searchPlaceholder": "Search Name or email"}, "joinGroupDetailsScreen": {"title": "Join Group", "joinGroup": "Join Group"}, "reviewsScreen": {"title": "Latest Reviews", "refineResults": "Refine Results", "searchPlaceholder": "Search Reviews", "results": "Results", "noReviewsFound": "No Reviews Found", "noReviewsFoundMessage": "There are no reviews available at the moment"}, "commentScreen": {"title": "Write a comment", "rating": "Rating", "headline": "Headline", "description": "Description", "addImage": "Add Image", "ratingRequired": "Rating is required", "selectRating": "Please select a rating", "headlineRequired": "Headline is required", "descriptionRequired": "Description is required", "maxCharacters": "Max 1500 characters", "uploadImagesAndVideos": "Upload images and videos", "writeHeadline": "Write a Headline", "describeExperience": "Describe your experience with this equipment. Include what was good and why, area for improvement, if any. 1500 character limit"}, "BiometricsScreen": {"title": "Authentification biométrique", "notAvailable": "L'authentification biométrique n'est pas disponible sur votre appareil.", "description": "Utilisez {getBiometryTypeText()} pour sécuriser votre application et éviter de saisir votre mot de passe à chaque ouverture de l'application.", "biometrics": "Biométrie", "faceId": "Face ID", "touchId": "Touch ID", "enable": "Enable", "whenEnabled": "When enabled,", "required": " will be required each time you open the app.", "protection": "Protection", "use": "Use", "toSecure": "to secure your app and avoid entering your password every time you open the app.", "biometricAuthenticationEnabled": "Biometric authentication has been enabled", "failedToEnableBiometricAuthentication": "Failed to enable biometric authentication", "biometricAuthenticationDisabled": "Biometric authentication has been disabled", "failedToDisableBiometricAuthentication": "Failed to disable biometric authentication", "errorOccurredWhileUpdatingBiometricSettings": "An error occurred while updating biometric settings"}, "NotificationScreen": {"title": "Notifications", "trash": "Trash", "more": "More", "notificationFunction": "Notification Function", "reply": "Reply", "remindMe": "Remind me"}, "equipmentReservationScreen": {"title": "Reserve or purchase equipment", "age": "Age", "availableNowInKiosk": "Available now in kiosk"}}