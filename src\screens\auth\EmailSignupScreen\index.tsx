import React, {useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '@navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {getGlobalStyles} from '@utils/styleUtils';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, CButton, Icon, SafeAreaView} from '@components/index';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {useAuthStore} from '@/store';
import {useRegister} from '@/hooks/queries/useAuth';
import {toaster, validatePassword} from '@/utils/commonFunctions';
type EmailSignupScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'EmailSignup'>;

// Form validation schema
const schema = yup.object({
  fullName: yup.string().required('Full name is required'),
  email: yup.string().email('Email format is invalid').required('Email is required'),
  password: yup
    .string()
    .required('Password is required')
    .test(
      'password-validation',
      'Password must have at least 8 characters, including uppercase, lowercase, a number, and a symbol',
      value => !!value && validatePassword(value).isValid,
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
});

// Form data type
type FormData = yup.InferType<typeof schema>;

const EmailSignupScreen = () => {
  const navigation = useNavigation<EmailSignupScreenNavigationProp>();
  const theme = useThemeStore();
  const globalStyles = getGlobalStyles({theme});
  const styles = createStyles(theme);
  const {isApiStatus} = useAuthStore();
  // Use React Query for registration
  const registerMutation = useRegister();

  // Add refs for input fields
  const fullNameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);

  // Initialize form with react-hook-form
  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const handleBack = () => {
    navigation.goBack();
  };

  const onSubmit = (data: FormData) => {
    // Form is valid, proceed with signup using React Query
    if (isApiStatus) {
      registerMutation.mutate(
        {
          email: data.email,
          password: data.password,
          fullName: data.fullName,
        },
        {
          onSuccess: response => {
            if (response.status) {
              if (response.data?.user_exist) {
                navigation.replace('Login', {email: data.email});
              } else {
                navigation.navigate('Verification', {
                  email: data.email,
                });
              }
              toaster('success', response.message, 'top');
            } else {
              toaster('error', response.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      navigation.navigate('Verification', {
        email: data.email,
      });
    }
  };
  const handleSignIn = () => {
    const email = control._formValues.email;
    navigation.replace('Login', {email});
  };

  return (
    <SafeAreaView includeTop={true} style={[styles.container]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled">
          <View style={styles.headerContainer}>
            <TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              activeOpacity={0.7}
              hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
              <Icon name="Left-chevron" size={22} color={theme.colors.gray} />
            </TouchableOpacity>
            <Typography variant="subtitle" style={globalStyles.title}>
              Sign up
            </Typography>
          </View>

          <View style={styles.form}>
            <View style={styles.flex}>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="fullName"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Full Name"
                      showLabel={true}
                      variant="dark"
                      placeholder="Enter your full name"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.fullName}
                      error={errors.fullName?.message}
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={fullNameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Email"
                      showLabel={true}
                      variant="dark"
                      placeholder="Enter your email"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => passwordInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="password"
                  render={({field: {onChange, onBlur, value}}) => {
                    const validation = validatePassword(value);

                    const errorMessage =
                      errors.password?.message ||
                      (value.length > 0 && !validation.isValid ? validation.message : '');

                    return (
                      <CInput
                        label="Password"
                        showLabel={true}
                        variant="dark"
                        placeholder="Create a password"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        hasError={!!errors.password || (value.length > 0 && !validation.isValid)}
                        error={errorMessage}
                        secureTextEntry
                        inputStyle={styles.input}
                        containerStyle={{marginBottom: 0}}
                        ref={passwordInputRef}
                        returnKeyType="next"
                        onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                        blurOnSubmit={false}
                      />
                    );
                  }}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="confirmPassword"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Confirm Password"
                      showLabel={true}
                      variant="dark"
                      placeholder="Confirm your password"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.confirmPassword}
                      error={errors.confirmPassword?.message}
                      secureTextEntry
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={confirmPasswordInputRef}
                      returnKeyType="done"
                      onSubmitEditing={handleSubmit(onSubmit)}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>
            </View>

            <CButton
              title="Next"
              onPress={handleSubmit(onSubmit)}
              loading={registerMutation.isPending}
              isDisabled={registerMutation.isPending}
            />

            <View style={styles.signInContainer}>
              <Text style={styles.signInText}>
                Already signed up?{' '}
                <Text style={styles.signInLink} onPress={handleSignIn}>
                  Sign in
                </Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EmailSignupScreen;
