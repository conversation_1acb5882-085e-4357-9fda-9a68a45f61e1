import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {SafeAreaView} from 'react-native-safe-area-context';
import withSwipeBack from '@/components/withSwipeBack';

const SimpleTestScreen = () => {
  const navigation = useNavigation();
  const theme = useThemeStore();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.primary,
      textAlign: 'center',
      marginBottom: 30,
    },
    instruction: {
      fontSize: 18,
      color: theme.colors.white,
      textAlign: 'center',
      marginBottom: 20,
      lineHeight: 26,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 15,
      borderRadius: 8,
      marginTop: 30,
      minWidth: 200,
    },
    buttonText: {
      color: theme.colors.black,
      fontSize: 16,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    highlight: {
      color: theme.colors.primary,
      fontWeight: 'bold',
    },
    successText: {
      color: theme.colors.primary,
      fontSize: 20,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>🎉 Success!</Text>

      <Text style={styles.successText}>You navigated to this screen successfully!</Text>

      <Text style={styles.instruction}>
        Now test the <Text style={styles.highlight}>swipe back gesture</Text>:
      </Text>

      <Text style={styles.instruction}>👆 Swipe from anywhere on the screen toward the right</Text>

      <Text style={styles.instruction}>
        The screen should slide back smoothly with custom gesture
      </Text>

      <TouchableOpacity style={styles.button} onPress={() => navigation.goBack()}>
        <Text style={styles.buttonText}>Or Go Back (Button)</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default withSwipeBack(SimpleTestScreen);
