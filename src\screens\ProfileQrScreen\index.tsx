import React, {useState, useRef, useEffect} from 'react';
import {View, StyleSheet, TouchableOpacity, Animated, Dimensions} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import QRGenerator from '@/components/QRGenerator';
import {useNavigation} from '@react-navigation/native';
import {setDarkStatusBar} from '@/utils/statusBarConfig';
import {useCallback} from 'react';
import {CImage, Header, Icon} from '@/components';
import {SafeAreaView} from '@/components/common';
import Typography from '@/components/Typography';
import {Images} from '@/config';

const ProfileQrScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();

  const styles = StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.black,
    },
    headerContainer: {
      backgroundColor: 'transparent',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
    },
    tabsWrapper: {
      paddingHorizontal: 16,
      paddingTop: 10,
      backgroundColor: 'transparent',
    },
    tabContainer: {
      flexDirection: 'row',
      height: 48,
      borderRadius: 25,
      overflow: 'hidden',
      backgroundColor: theme.colors.jetBlack,
      padding: 4,
    },
    tabButton: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      marginHorizontal: 2,
      borderRadius: 25,
    },
    activeTabButton: {
      backgroundColor: theme.colors.lime,
    },
    inactiveTabButton: {
      backgroundColor: theme.colors.jetBlack,
    },
    tabText: {
      fontWeight: 'bold',
      fontSize: 14,
    },
    activeTabText: {
      color: theme.colors.black,
    },
    inactiveTabText: {
      color: theme.colors.white,
    },
    content: {
      flex: 1,
      backgroundColor: 'transparent',
      justifyContent: 'center',
      alignItems: 'center',
    },

    helpContainer: {
      alignItems: 'center',
      paddingVertical: 16,
      backgroundColor: 'transparent',
      width: '100%',
    },
  });

  // Initial setup
  useEffect(() => {
    // Set dark status bar (black background with light content)
    setDarkStatusBar();

    // For react-navigation status bar options
    navigation.setOptions({
      headerShown: false,
      statusBarColor: theme.colors.black,
      statusBarStyle: 'light-content',
    });
  }, [navigation, theme.colors.black]);

  // Make sure status bar is black when screen is focused
  useCallback(() => {
    // This runs when the screen comes into focus
    setDarkStatusBar();

    // Optional: Clean up when screen loses focus
    return () => {
      // If you want to reset to default when screen is unfocused
      // (usually not needed as the next screen will set its own)
    };
  }, []);

  // Animation references for content fading
  const contentOpacity = useRef({
    pickup: new Animated.Value(1),
    direct: new Animated.Value(0),
  }).current;

  return (
    <SafeAreaView includeTop={false} style={styles.container}>
      <View style={[styles.container]}>
        <Header
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.navigate('EditProfile')}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          leftIconButtonStyle={styles.menuButton}
          backgroundColor="transparent"
          transparent={true}
          containerStyle={styles.headerContainer}
          headerStyle={{borderBottomWidth: 0}}
        />

        <View style={styles.tabsWrapper}>
          <Typography variant="parkTitle" align="center" color={theme.colors.white}>
            Quick Connect
          </Typography>
          <Typography variant="subtitle" align="center" color={theme.colors.white}>
            Scan QR code
          </Typography>
        </View>

        <View style={styles.content}>
          <Typography variant="qrName" align="center" color={theme.colors.white}>
            Alex James
          </Typography>
          <QRGenerator size={240} showText={false} style={{marginTop: 10, marginBottom: 10}} />

          <CImage source={Images.goRactLogo} style={{width: 165, height: 53}} />
        </View>

        <View style={styles.helpContainer}>
          <TouchableOpacity onPress={() => navigation.navigate('Help')}>
            <Typography variant="subTitle3" color={theme.colors.lime}>
              Need help?
            </Typography>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ProfileQrScreen;
